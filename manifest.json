{
    // 应用名称
    "name" : "sl-express-courier",
    // DCloud应用标识
    "appid" : "",
    // 应用描述
    "description" : "",
    // 版本名称
    "versionName" : "1.0.0",
    // 版本号
    "versionCode" : "100",
    // 是否转换px单位为rpx
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        // 是否启用自定义组件
        "usingComponents" : true,
        // nvue页面编译方式
        "nvueStyleCompiler" : "uni-app",
        // 编译器版本
        "compilerVersion" : 3,
        // 启动界面配置
        "splashscreen" : {
            // 是否在渲染前始终显示启动界面
            "alwaysShowBeforeRender" : true,
            // 是否显示等待圈
            "waiting" : true,
            // 是否自动关闭启动界面
            "autoclose" : true,
            // 延迟时间
            "delay" : 0
        },
        // 状态栏配置
        "statusbar" : {
            // 沉浸式状态栏
            "immersed" : "supportedDevice",
            // 状态栏样式
            "style" : "light",
            // 状态栏背景色
            "background" : "#3498db"
        },
        /* 模块配置 */
        "modules" : {},
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {},
            /* SDK配置 */
            "sdkConfigs" : {}
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        // 微信小程序appid
        "appid" : "",
        "setting" : {
            // 是否检查安全域名
            "urlCheck" : false
        },
        // 是否启用自定义组件
        "usingComponents" : true
    },
    "mp-alipay" : {
        // 是否启用自定义组件
        "usingComponents" : true
    },
    "mp-baidu" : {
        // 是否启用自定义组件
        "usingComponents" : true
    },
    "mp-toutiao" : {
        // 是否启用自定义组件
        "usingComponents" : true
    },
    // uni统计配置
    "uniStatistics" : {
        // 是否启用统计
        "enable" : false
    },
    // Vue版本
    "vueVersion" : "3"
}
