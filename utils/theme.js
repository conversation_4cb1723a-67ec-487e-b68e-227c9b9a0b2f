/**
 * 主题管理工具类
 * 提供应用程序主题切换和管理功能
 */
class ThemeManager {
  /**
   * 构造函数
   * 初始化主题管理器
   */
  constructor() {
    // 支持的主题列表
    this.themes = ['default', 'dark', 'green', 'purple'];
    // 默认主题
    this.currentTheme = 'default';
  }

  /**
   * 切换主题
   * @param {string} theme - 要切换的主题名称
   */
  switchTheme(theme) {
    console.log('Attempting to switch theme to:', theme);
    // 检查主题是否支持
    if (!this.themes.includes(theme)) {
      console.warn(`Theme '${theme}' is not supported.`);
      return;
    }

    // 在uni-app中通过CSS变量方式切换主题
    try {
      // 获取应用实例（兼容不同环境）
      const app = typeof getApp !== 'undefined' ? getApp() : null;
      
      // 方式1: 直接操作document.documentElement（最直接有效的方法）
      if (typeof document !== 'undefined' && document.documentElement) {
        console.log('Using document.documentElement for theme application');
        // 先移除所有主题属性
        document.documentElement.removeAttribute('data-theme');
        // 应用新主题
        if (theme !== 'default') {
          document.documentElement.setAttribute('data-theme', theme);
        }
        
        console.log('Theme attribute set:', document.documentElement.getAttribute('data-theme'));
        
        // 强制重新渲染
        const forceRerender = document.createElement('style');
        forceRerender.textContent = `body { opacity: 0.99; }`;
        document.head.appendChild(forceRerender);
        setTimeout(() => {
          document.head.removeChild(forceRerender);
        }, 10);
        
        console.log('Theme applied via document.documentElement');
      }
      // 方式2: 尝试获取应用实例并操作根元素
      else if (app && app.$vm && app.$vm.$el) {
        app.$vm.$el.removeAttribute('data-theme');
        if (theme !== 'default') {
          app.$vm.$el.setAttribute('data-theme', theme);
        }
      }
      else {
        console.warn('Unable to apply theme, fallback to manual CSS update');
        // 方式3: 手动应用CSS变量（备用方法）
        this.updateCSSVariablesManually(theme);
      }
      
      // 更新当前主题
      this.currentTheme = theme;
      console.log('Current theme updated to:', this.currentTheme);
      
      // 保存到本地存储
      try {
        uni.setStorageSync('app-theme', theme);
        console.log('Theme saved to storage');
      } catch (e) {
        console.error('Failed to save theme to storage:', e);
      }
    } catch (e) {
      console.error('Theme application failed:', e);
    }

    // 更新状态栏颜色
    this.updateStatusBar(theme);
  }

  /**
   * 更新状态栏样式
   * @param {string} theme - 主题名称
   */
  updateStatusBar(theme) {
    // 定义不同主题的状态栏配置
    const statusBarConfig = {
      default: {
        backgroundColor: '#3498db',
        style: 'light'
      },
      dark: {
        backgroundColor: '#1a1a2e',
        style: 'light'
      },
      green: {
        backgroundColor: '#27ae60',
        style: 'light'
      },
      purple: {
        backgroundColor: '#9b59b6',
        style: 'light'
      }
    };

    const config = statusBarConfig[theme] || statusBarConfig.default;

    // 更新状态栏样式（仅在App端有效）
    // #ifdef APP-PLUS
    try {
      if (typeof plus !== 'undefined' && plus.navigator) {
        plus.navigator.setStatusBarStyle(config.style);
        plus.navigator.setStatusBarBackground(config.backgroundColor);
      }
    } catch (e) {
      console.warn('Failed to update status bar:', e);
    }
    // #endif
  }

  /**
   * 获取当前主题
   * @returns {string} 当前主题名称
   */
  getCurrentTheme() {
    return this.currentTheme;
  }

  /**
   * 初始化主题
   * 从本地存储加载保存的主题
   */
  init() {
    try {
      // 从本地存储获取主题
      const savedTheme = uni.getStorageSync('app-theme') || 'default';
      this.currentTheme = savedTheme;
      this.switchTheme(savedTheme);

      // 初始化状态栏
      this.updateStatusBar(savedTheme);
    } catch (e) {
      console.error('Failed to load theme from storage:', e);
      this.switchTheme('default');
      this.updateStatusBar('default');
    }
  }

  /**
   * 获取所有支持的主题
   * @returns {Array} 主题列表
   */
  getThemes() {
    return this.themes;
  }
  
  /**
   * 手动更新CSS变量（备用方案）
   * @param {string} theme - 主题名称
   */
  updateCSSVariablesManually(theme) {
    if (typeof document !== 'undefined' && document.documentElement) {
      // 定义各主题的CSS变量
      const themeVariables = {
        default: {
          '--primary-color': '#3498db',
          '--secondary-color': '#2c3e50',
          '--accent-color': '#1abc9c'
        },
        dark: {
          '--primary-color': '#1a1a2e',
          '--secondary-color': '#16213e',
          '--accent-color': '#1abc9c'
        },
        green: {
          '--primary-color': '#2ecc71',
          '--secondary-color': '#27ae60',
          '--accent-color': '#f1c40f'
        },
        purple: {
          '--primary-color': '#9b59b6',
          '--secondary-color': '#8e44ad',
          '--accent-color': '#e74c3c'
        }
      };
      
      const variables = themeVariables[theme] || themeVariables.default;
      
      // 应用CSS变量
      Object.keys(variables).forEach(key => {
        document.documentElement.style.setProperty(key, variables[key]);
      });
    }
  }
}

// 创建单例实例
const themeManager = new ThemeManager();

// 导出实例
export default themeManager;
