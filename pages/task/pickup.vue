<template>
  <view class="pickup-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <text class="header-title">去取件</text>
      <view class="header-right">
        <text class="more-icon">⋯</text>
      </view>
    </view>
    
    <!-- 订单信息 -->
    <view class="order-info">
      <view class="section-title">订单信息</view>
      <view class="info-item">
        <text class="label">订单号:</text>
        <text class="value">SF1234567890</text>
      </view>
      <view class="info-item">
        <text class="label">下单时间:</text>
        <text class="value">2023-04-01 09:30</text>
      </view>
      <view class="info-item">
        <text class="label">预约时间:</text>
        <text class="value">2023-04-01 10:00-12:00</text>
      </view>
    </view>
    
    <!-- 物品信息 -->
    <view class="package-info">
      <view class="section-title">物品信息</view>
      <view class="info-item">
        <text class="label">物品类型:</text>
        <text class="value">文件</text>
      </view>
      <view class="info-item">
        <text class="label">物品重量:</text>
        <text class="value">0.5kg</text>
      </view>
      <view class="info-item">
        <text class="label">物品数量:</text>
        <text class="value">1件</text>
      </view>
      <view class="info-item">
        <text class="label">备注信息:</text>
        <text class="value">重要文件，请妥善保管</text>
      </view>
    </view>
    
    <!-- 费用与支付 -->
    <view class="payment-info">
      <view class="section-title">费用与支付</view>
      <view class="fee-item">
        <text class="label">运费:</text>
        <text class="value">¥8.00</text>
      </view>
      <view class="fee-item">
        <text class="label">保价费:</text>
        <text class="value">¥2.00</text>
      </view>
      <view class="fee-item total">
        <text class="label">合计:</text>
        <text class="value total-value">¥10.00</text>
      </view>
      <view class="payment-method">
        <text class="label">支付方式:</text>
        <view class="method-selector">
          <view class="method-item active">在线支付</view>
          <view class="method-item">货到付款</view>
        </view>
      </view>
    </view>
    
    <!-- 身份验证 -->
    <view class="identity-verification">
      <view class="section-title">身份验证</view>
      <view class="verification-method">
        <view class="method-item active">短信验证码</view>
        <view class="method-item">身份证核验</view>
      </view>
      <view class="verification-input">
        <input class="code-input" type="number" placeholder="请输入验证码">
        <button class="send-code-btn">发送验证码</button>
      </view>
    </view>
    
    <!-- 备注与照片 -->
    <view class="remark-photos">
      <view class="section-title">备注与照片</view>
      <textarea class="remark-textarea" placeholder="请输入备注信息"></textarea>
      <view class="photo-upload">
        <view class="upload-area">
          <text class="upload-icon">+</text>
          <text class="upload-text">上传照片</text>
        </view>
      </view>
    </view>
    
    <!-- 核心操作 -->
    <view class="action-section">
      <button class="action-btn confirm-btn" @click="confirmPickup">确认取件</button>
      <button class="action-btn cancel-btn" @click="cancelPickup">取消订单</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 页面数据
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    confirmPickup() {
      uni.showModal({
        title: '确认取件',
        content: '确认已完成取件操作？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '取件成功',
              icon: 'success'
            });
            
            // 返回上一页
            setTimeout(() => {
              uni.navigateBack();
            }, 1000);
          }
        }
      });
    },
    cancelPickup() {
      uni.showModal({
        title: '取消订单',
        content: '确定要取消该订单吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '订单已取消',
              icon: 'success'
            });
            
            // 返回上一页
            setTimeout(() => {
              uni.navigateBack();
            }, 1000);
          }
        }
      });
    }
  }
}
</script>

<style scoped>
/* 容器 */
.pickup-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 28rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.more-icon {
  font-size: 36rpx;
}

/* 内容区域 */
.order-info,
.package-info,
.payment-info,
.identity-verification,
.remark-photos {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.info-item,
.fee-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-item:last-child,
.fee-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 26rpx;
  color: var(--text-gray);
}

.value {
  font-size: 26rpx;
  color: var(--text-dark);
}

.fee-item.total {
  border-top: 1rpx solid var(--border-color);
  padding-top: 20rpx;
  margin-top: 20rpx;
  font-weight: bold;
}

.total-value {
  color: #e74c3c;
  font-size: 32rpx;
}

/* 支付方式 */
.payment-method {
  margin-top: 30rpx;
}

.method-selector {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.method-item {
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  color: var(--text-dark);
}

.method-item.active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

/* 身份验证 */
.verification-method {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.verification-input {
  display: flex;
  gap: 20rpx;
}

.code-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 10rpx;
  box-sizing: border-box;
}

.send-code-btn {
  width: 200rpx;
  height: 70rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
  font-size: 26rpx;
  border-radius: 10rpx;
  border: none;
}

/* 备注与照片 */
.remark-textarea {
  width: 100%;
  height: 120rpx;
  padding: 20rpx;
  font-size: 26rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 10rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150rpx;
  border: 2rpx dashed var(--border-color);
  border-radius: 10rpx;
}

.upload-icon {
  font-size: 48rpx;
  color: var(--text-gray);
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
  color: var(--text-gray);
}

/* 核心操作 */
.action-section {
  padding: 20rpx 30rpx;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 10rpx;
  border: none;
  margin-bottom: 20rpx;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.confirm-btn {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.cancel-btn {
  background-color: var(--bg-gray);
  color: var(--text-dark);
}
</style>