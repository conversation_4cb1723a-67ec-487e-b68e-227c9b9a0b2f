<template>
  <view class="delivery-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <text class="header-title">去派送</text>
      <view class="header-right">
        <text class="more-icon">⋯</text>
      </view>
    </view>
    
    <!-- 订单信息 -->
    <view class="order-info">
      <view class="section-title">订单信息</view>
      <view class="info-item">
        <text class="label">订单号:</text>
        <text class="value">SF1234567890</text>
      </view>
      <view class="info-item">
        <text class="label">下单时间:</text>
        <text class="value">2023-04-01 09:30</text>
      </view>
      <view class="info-item">
        <text class="label">预约时间:</text>
        <text class="value">2023-04-01 15:00-17:00</text>
      </view>
    </view>
    
    <!-- 收货信息 -->
    <view class="recipient-info">
      <view class="section-title">收货信息</view>
      <view class="info-item">
        <text class="label">收货人:</text>
        <text class="value">张三</text>
      </view>
      <view class="info-item">
        <text class="label">联系电话:</text>
        <text class="value">138****8888</text>
      </view>
      <view class="info-item">
        <text class="label">收货地址:</text>
        <text class="value">北京市朝阳区某某街道某某小区1号楼101室</text>
      </view>
    </view>
    
    <!-- 物品信息 -->
    <view class="package-info">
      <view class="section-title">物品信息</view>
      <view class="info-item">
        <text class="label">物品类型:</text>
        <text class="value">文件</text>
      </view>
      <view class="info-item">
        <text class="label">物品重量:</text>
        <text class="value">0.5kg</text>
      </view>
      <view class="info-item">
        <text class="label">物品数量:</text>
        <text class="value">1件</text>
      </view>
    </view>
    
    <!-- 签收人选择 -->
    <view class="signatory-selection">
      <view class="section-title">签收人</view>
      <view class="signatory-options">
        <view class="option-item active">本人签收</view>
        <view class="option-item">代收点</view>
        <view class="option-item">快递柜</view>
      </view>
      <input class="signatory-input" type="text" placeholder="请输入签收人姓名">
    </view>
    
    <!-- 核心操作 -->
    <view class="action-section">
      <button class="action-btn confirm-btn" @click="confirmDelivery">确认签收</button>
      <button class="action-btn exception-btn" @click="reportException">异常上报</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 页面数据
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    confirmDelivery() {
      uni.showModal({
        title: '确认签收',
        content: '确认客户已签收？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '签收成功',
              icon: 'success'
            });
            
            // 返回上一页
            setTimeout(() => {
              uni.navigateBack();
            }, 1000);
          }
        }
      });
    },
    reportException() {
      uni.navigateTo({
        url: '/pages/task/exception'
      });
    }
  }
}
</script>

<style scoped>
/* 容器 */
.delivery-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 28rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.more-icon {
  font-size: 36rpx;
}

/* 内容区域 */
.order-info,
.recipient-info,
.package-info,
.signatory-selection {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 26rpx;
  color: var(--text-gray);
}

.value {
  font-size: 26rpx;
  color: var(--text-dark);
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

/* 签收人选择 */
.signatory-options {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.option-item {
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  color: var(--text-dark);
}

.option-item.active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.signatory-input {
  width: 100%;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 10rpx;
  box-sizing: border-box;
}

/* 核心操作 */
.action-section {
  padding: 20rpx 30rpx;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 10rpx;
  border: none;
  margin-bottom: 20rpx;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.confirm-btn {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.exception-btn {
  background-color: #e74c3c;
  color: var(--text-white);
}
</style>