<template>
  <view class="task-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <text class="header-title">派件列表</text>
      <view class="header-right">
        <text class="refresh-btn" @click="refreshTasks">刷新</text>
      </view>
    </view>
    
    <!-- 状态切换标签 -->
    <view class="status-tabs">
      <view 
        class="tab-item" 
        :class="{ active: currentStatus === 'pending' }" 
        @click="switchStatus('pending')">
        待派件
      </view>
      <view 
        class="tab-item" 
        :class="{ active: currentStatus === 'completed' }" 
        @click="switchStatus('completed')">
        已签收
      </view>
    </view>
    
    <!-- 筛选与排序 -->
    <view class="filter-sort-container">
      <view class="filter-options">
        <picker @change="onSortChange" :value="sortIndex" :range="sortOptions" range-key="label">
          <view class="filter-btn">
            <text>排序: {{ sortOptions[sortIndex].label }}</text>
            <text class="arrow">▼</text>
          </view>
        </picker>
      </view>
      <view class="search-box" @click="toggleSearch">
        <text class="search-icon">🔍</text>
        <text class="search-placeholder">搜索任务</text>
      </view>
    </view>
    
    <!-- 任务列表 -->
    <scroll-view class="task-list" scroll-y="true">
      <view class="task-item" v-for="task in filteredTasks" :key="task.id" @click="goToTaskDetail(task.id)">
        <view class="task-header">
          <text class="task-id">任务单号: {{ task.id }}</text>
          <text class="task-status" :class="task.statusClass">{{ task.statusText }}</text>
        </view>
        <view class="task-content">
          <view class="task-address">
            <text class="label">收货地址:</text>
            <text class="address">{{ task.deliveryAddress }}</text>
          </view>
          <view class="task-time">
            <text class="label">派送时间:</text>
            <text class="time">{{ task.deliveryTime }}</text>
          </view>
          <view class="task-customer">
            <text class="label">收货人:</text>
            <text class="customer">{{ task.customerName }} {{ task.customerPhone }}</text>
          </view>
        </view>
        <view class="task-footer">
          <text class="distance">{{ task.distance }}km</text>
          <button class="action-btn" :class="task.actionClass" @click.stop="handleTaskAction(task)">{{ task.actionText }}</button>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredTasks.length === 0">
        <text class="empty-text">暂无任务</text>
      </view>
    </scroll-view>
    
    <!-- 批量管理 -->
    <view class="batch-management" v-if="showBatchManagement">
      <view class="batch-actions">
        <label class="selectAll">
          <checkbox :checked="allSelected" @click="toggleSelectAll"></checkbox>
          <text>全选</text>
        </label>
        <view class="action-buttons">
          <button class="batch-btn" @click="batchComplete">批量完成</button>
          <button class="batch-btn delete" @click="batchDelete">批量删除</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentStatus: 'pending', // 当前状态: pending(待派件), completed(已签收)
      sortIndex: 0, // 当前排序索引
      showBatchManagement: false, // 是否显示批量管理
      allSelected: false, // 是否全选
      sortOptions: [
        { label: '按时间排序', value: 'time' },
        { label: '按距离排序', value: 'distance' },
        { label: '超时任务优先', value: 'overdue' }
      ],
      tasks: [
        {
          id: 'T20230401004',
          status: 'pending',
          statusText: '待派送',
          statusClass: 'status-pending',
          deliveryAddress: '北京市朝阳区某某街道某某小区1号楼101室',
          deliveryTime: '2023-04-01 15:00-17:00',
          customerName: '张先生',
          customerPhone: '138****8888',
          distance: '3.2',
          actionText: '去派送',
          actionClass: 'action-delivery',
          selected: false
        },
        {
          id: 'T20230401005',
          status: 'pending',
          statusText: '待派送',
          statusClass: 'status-pending',
          deliveryAddress: '北京市海淀区某某科技园A座508室',
          deliveryTime: '2023-04-01 16:00-18:00',
          customerName: '刘女士',
          customerPhone: '139****9999',
          distance: '6.7',
          actionText: '去派送',
          actionClass: 'action-delivery',
          selected: false
        },
        {
          id: 'T20230401006',
          status: 'completed',
          statusText: '已签收',
          statusClass: 'status-completed',
          deliveryAddress: '北京市东城区某某胡同3号院',
          deliveryTime: '2023-04-01 11:00-13:00',
          customerName: '陈先生',
          customerPhone: '136****6666',
          distance: '4.1',
          actionText: '已完成',
          actionClass: 'action-completed',
          selected: false
        }
      ]
    }
  },
  computed: {
    filteredTasks() {
      // 根据当前状态过滤任务
      return this.tasks.filter(task => {
        if (this.currentStatus === 'pending') {
          return task.status === 'pending';
        } else if (this.currentStatus === 'completed') {
          return task.status === 'completed';
        }
        return true;
      });
    }
  },
  methods: {
    switchStatus(status) {
      this.currentStatus = status;
    },
    onSortChange(e) {
      this.sortIndex = e.detail.value;
      // 实际项目中这里会重新排序任务列表
      console.log('排序方式:', this.sortOptions[this.sortIndex].label);
    },
    refreshTasks() {
      // 刷新任务列表
      uni.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    },
    goToTaskDetail(taskId) {
      // 跳转到任务详情页
      uni.navigateTo({
        url: `/pages/task/detail.vue?id=${taskId}`
      });
    },
    handleTaskAction(task) {
      // 处理任务操作
      if (task.status === 'pending') {
        // 跳转到去派件页面
        uni.navigateTo({
          url: `/pages/task/delivery.vue?id=${task.id}`
        });
      }
    },
    toggleBatchManagement() {
      this.showBatchManagement = !this.showBatchManagement;
      if (!this.showBatchManagement) {
        // 退出批量管理时重置选择状态
        this.tasks.forEach(task => task.selected = false);
        this.allSelected = false;
      }
    },
    goBack() {
      uni.navigateBack();
    }
    toggleSelectAll() {
      this.allSelected = !this.allSelected;
      this.filteredTasks.forEach(task => task.selected = this.allSelected);
    },
    batchComplete() {
      // 批量完成操作
      uni.showToast({
        title: '批量完成操作',
        icon: 'none'
      });
    },
    batchDelete() {
      // 批量删除操作
      uni.showModal({
        title: '确认删除',
        content: '确定要删除选中的任务吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
            // 实际项目中这里会执行删除操作
          }
        }
      });
    },
    toggleSearch() {
      // 切换搜索框显示
      console.log('切换搜索框显示');
    }
  }
}
</script>

<style scoped>
/* 任务容器 */
.task-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.refresh-btn {
  font-size: 28rpx;
}

/* 状态标签 */
.status-tabs {
  display: flex;
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-color);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: var(--text-gray);
}

.tab-item.active {
  color: var(--primary-color);
  border-bottom: 4rpx solid var(--primary-color);
}

/* 筛选与排序 */
.filter-sort-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-color);
}

.filter-options {
  flex: 1;
}

.filter-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 20rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  font-size: 26rpx;
}

.arrow {
  margin-left: 10rpx;
  font-size: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
}

.search-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
}

.search-placeholder {
  font-size: 26rpx;
  color: var(--text-gray);
}

/* 任务列表 */
.task-list {
  flex: 1;
}

.task-item {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
  padding: 20rpx;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.task-id {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.task-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  background-color: var(--bg-gray);
  color: var(--text-white);
}

.task-status.status-pending {
  background-color: #f39c12;
}

.task-status.status-completed {
  background-color: #27ae60;
}

.task-content {
  margin-bottom: 20rpx;
}

.label {
  font-size: 24rpx;
  color: var(--text-gray);
  margin-right: 10rpx;
}

.address, .time, .customer {
  font-size: 26rpx;
  color: var(--text-dark);
  margin-bottom: 10rpx;
  display: block;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.distance {
  font-size: 24rpx;
  color: var(--primary-color);
}

.action-btn {
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
}

.action-btn.action-delivery {
  background-color: #3498db;
}

.action-btn.action-completed {
  background-color: var(--bg-gray);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-gray);
}

/* 批量管理 */
.batch-management {
  position: fixed;
  bottom: 120rpx;
  left: 0;
  right: 0;
  background-color: var(--bg-white);
  border-top: 1rpx solid var(--border-color);
  padding: 20rpx 30rpx;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selectAll {
  display: flex;
  align-items: center;
}

.selectAll text {
  margin-left: 10rpx;
  font-size: 28rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.batch-btn {
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
}

.batch-btn.delete {
  background-color: var(--text-error);
}

</style>