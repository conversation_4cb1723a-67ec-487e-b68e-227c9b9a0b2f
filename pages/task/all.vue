<template>
  <view class="all-tasks-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <text class="header-title">全部取派</text>
      <view class="header-right">
        <text class="date-selector" @click="selectDate">📅</text>
      </view>
    </view>
    
    <!-- 日期筛选 -->
    <view class="date-filter">
      <view class="date-display">
        <text class="date-text">{{ selectedDate }}</text>
      </view>
    </view>
    
    <!-- 任务统计 -->
    <view class="task-stats">
      <view class="stat-item">
        <text class="stat-value">{{ pickupCount }}</text>
        <text class="stat-label">取件任务</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ deliveryCount }}</text>
        <text class="stat-label">派件任务</text>
      </view>
    </view>
    
    <!-- 日期筛选 -->
    <view class="date-filter">
      <view class="date-display">
        <text class="date-text">{{ selectedDate }}</text>
      </view>
    </view>
    
    <!-- 任务统计 -->
    <view class="task-stats">
      <view class="stat-item">
        <text class="stat-value">{{ pickupCount }}</text>
        <text class="stat-label">取件任务</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ deliveryCount }}</text>
        <text class="stat-label">派件任务</text>
      </view>
    </view>
    
    <!-- 任务列表 -->
    <scroll-view class="task-list" scroll-y="true">
      <view class="task-item" v-for="task in allTasks" :key="task.id" @click="goToTaskDetail(task.id)">
        <view class="task-header">
          <text class="task-id">任务单号: {{ task.id }}</text>
          <text class="task-status" :class="task.statusClass">{{ task.statusText }}</text>
        </view>
        <view class="task-content">
          <view class="task-address" v-if="task.type === 'pickup'">
            <text class="label">寄件地址:</text>
            <text class="address">{{ task.pickupAddress }}</text>
          </view>
          <view class="task-address" v-else>
            <text class="label">收货地址:</text>
            <text class="address">{{ task.deliveryAddress }}</text>
          </view>
          <view class="task-time">
            <text class="label">{{ task.type === 'pickup' ? '预约时间:' : '派送时间:' }}</text>
            <text class="time">{{ task.type === 'pickup' ? task.pickupTime : task.deliveryTime }}</text>
          </view>
          <view class="task-customer" v-if="task.type === 'pickup'">
            <text class="label">寄件人:</text>
            <text class="customer">{{ task.senderName }} {{ task.senderPhone }}</text>
          </view>
          <view class="task-customer" v-else>
            <text class="label">收货人:</text>
            <text class="customer">{{ task.customerName }} {{ task.customerPhone }}</text>
          </view>
        </view>
        <view class="task-footer">
          <text class="distance">{{ task.distance }}km</text>
          <button class="action-btn" :class="task.actionClass" @click.stop="handleTaskAction(task)">{{ task.actionText }}</button>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="allTasks.length === 0">
        <text class="empty-text">暂无任务</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedDate: '2023-04-01', // 默认选中日期
      pickupTasks: [
        {
          id: 'T20230401001',
          type: 'pickup',
          status: 'pending',
          statusText: '待取件',
          statusClass: 'status-pending',
          pickupAddress: '北京市海淀区某某大厦202室',
          pickupTime: '2023-04-01 10:00-12:00',
          senderName: '李女士',
          senderPhone: '139****9999',
          distance: '2.5',
          actionText: '去取件',
          actionClass: 'action-pickup'
        },
        {
          id: 'T20230401002',
          type: 'pickup',
          status: 'completed',
          statusText: '已取件',
          statusClass: 'status-completed',
          pickupAddress: '北京市朝阳区某某小区3号楼501室',
          pickupTime: '2023-04-01 14:00-16:00',
          senderName: '王先生',
          senderPhone: '138****8888',
          distance: '5.2',
          actionText: '已完成',
          actionClass: 'action-completed'
        }
      ],
      deliveryTasks: [
        {
          id: 'T20230401004',
          type: 'delivery',
          status: 'pending',
          statusText: '待派送',
          statusClass: 'status-pending',
          deliveryAddress: '北京市朝阳区某某街道某某小区1号楼101室',
          deliveryTime: '2023-04-01 15:00-17:00',
          customerName: '张先生',
          customerPhone: '138****8888',
          distance: '3.2',
          actionText: '去派送',
          actionClass: 'action-delivery'
        },
        {
          id: 'T20230401005',
          type: 'delivery',
          status: 'completed',
          statusText: '已签收',
          statusClass: 'status-completed',
          deliveryAddress: '北京市海淀区某某科技园A座508室',
          deliveryTime: '2023-04-01 16:00-18:00',
          customerName: '刘女士',
          customerPhone: '139****9999',
          distance: '6.7',
          actionText: '已完成',
          actionClass: 'action-completed'
        }
      ]
    }
  },
  computed: {
    allTasks() {
      // 合并取件和派件任务
      return [...this.pickupTasks, ...this.deliveryTasks];
    },
    pickupCount() {
      return this.pickupTasks.length;
    },
    deliveryCount() {
      return this.deliveryTasks.length;
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    selectDate() {
      // 选择日期功能
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    goToTaskDetail(taskId) {
      // 跳转到任务详情页
      uni.navigateTo({
        url: `/pages/task/detail.vue?id=${taskId}`
      });
    },
    handleTaskAction(task) {
      // 处理任务操作
      if (task.type === 'pickup' && task.status === 'pending') {
        // 跳转到去取件页面
        uni.navigateTo({
          url: `/pages/task/pickup.vue?id=${task.id}`
        });
      } else if (task.type === 'delivery' && task.status === 'pending') {
        // 跳转到去派件页面
        uni.navigateTo({
          url: `/pages/task/delivery.vue?id=${task.id}`
        });
      }
    }
  }
}
</script>

<style scoped>
/* 容器 */
.all-tasks-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 28rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.date-selector {
  font-size: 36rpx;
}

/* 日期筛选 */
.date-filter {
  background-color: var(--bg-white);
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.date-display {
  text-align: center;
}

.date-text {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
}

/* 任务统计 */
.task-stats {
  display: flex;
  background-color: var(--bg-white);
  padding: 30rpx;
  margin: 20rpx 0;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 28rpx;
  color: var(--text-gray);
}

/* 任务列表 */
.task-list {
  flex: 1;
}

.task-item {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
  padding: 20rpx;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.task-id {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.task-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  background-color: var(--bg-gray);
  color: var(--text-white);
}

.task-status.status-pending {
  background-color: #f39c12;
}

.task-status.status-completed {
  background-color: #27ae60;
}

.task-content {
  margin-bottom: 20rpx;
}

.label {
  font-size: 24rpx;
  color: var(--text-gray);
  margin-right: 10rpx;
}

.address, .time, .customer {
  font-size: 26rpx;
  color: var(--text-dark);
  margin-bottom: 10rpx;
  display: block;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.distance {
  font-size: 24rpx;
  color: var(--primary-color);
}

.action-btn {
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
}

.action-btn.action-pickup {
  background-color: var(--accent-color);
}

.action-btn.action-delivery {
  background-color: #3498db;
}

.action-btn.action-completed {
  background-color: var(--bg-gray);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-gray);
}
</style>