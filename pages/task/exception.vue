<template>
  <view class="exception-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <text class="header-title">异常上报</text>
      <view class="header-right"></view>
    </view>
    
    <!-- 异常类型 -->
    <view class="exception-type">
      <view class="section-title">异常类型</view>
      <view class="type-list">
        <view 
          class="type-item" 
          v-for="type in exceptionTypes" 
          :key="type.value"
          :class="{ active: selectedType === type.value }"
          @click="selectType(type.value)"
        >
          {{ type.label }}
        </view>
      </view>
    </view>
    
    <!-- 异常描述 -->
    <view class="exception-description">
      <view class="section-title">异常描述</view>
      <textarea 
        class="description-textarea" 
        v-model="exceptionDescription" 
        placeholder="请详细描述异常情况，以便我们及时处理">
      </textarea>
    </view>
    
    <!-- 图片上传 -->
    <view class="image-upload">
      <view class="section-title">图片上传</view>
      <view class="upload-grid">
        <view class="upload-item" v-for="(image, index) in uploadedImages" :key="index">
          <image class="uploaded-image" :src="image" mode="aspectFill"></image>
          <text class="remove-image" @click="removeImage(index)">✕</text>
        </view>
        <view class="upload-item add-more" @click="addImage" v-if="uploadedImages.length < 3">
          <text class="upload-icon">+</text>
          <text class="upload-text">添加图片</text>
        </view>
      </view>
    </view>
    
    <!-- 联系方式 -->
    <view class="contact-info">
      <view class="section-title">联系方式</view>
      <input 
        class="contact-input" 
        type="text" 
        v-model="contactInfo" 
        placeholder="请输入您的联系方式，方便我们与您联系">
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" @click="submitException">提交异常</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedType: 'delivery_failed',
      exceptionTypes: [
        { label: '派送失败', value: 'delivery_failed' },
        { label: '物品损坏', value: 'item_damaged' },
        { label: '地址错误', value: 'address_error' },
        { label: '客户拒收', value: 'customer_refused' },
        { label: '其他异常', value: 'other' }
      ],
      exceptionDescription: '',
      uploadedImages: [],
      contactInfo: ''
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    selectType(type) {
      this.selectedType = type;
    },
    addImage() {
      // 模拟添加图片
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.uploadedImages.push(res.tempFilePaths[0]);
        }
      });
    },
    removeImage(index) {
      this.uploadedImages.splice(index, 1);
    },
    submitException() {
      // 验证必填信息
      if (!this.exceptionDescription) {
        uni.showToast({
          title: '请输入异常描述',
          icon: 'none'
        });
        return;
      }
      
      if (!this.contactInfo) {
        uni.showToast({
          title: '请输入联系方式',
          icon: 'none'
        });
        return;
      }
      
      // 提交异常
      uni.showLoading({
        title: '提交中...'
      });
      
      // 模拟提交过程
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '提交成功',
          icon: 'success'
        });
        
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1000);
      }, 1500);
    }
  }
}
</script>

<style scoped>
/* 容器 */
.exception-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #3498db;
  color: #ffffff;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 28rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.header-right {
  width: 80rpx;
}

/* 内容区域 */
.exception-type,
.exception-description,
.image-upload,
.contact-info {
  background-color: #ffffff;
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eeeeee;
}

/* 异常类型 */
.type-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.type-item {
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  color: #333333;
}

.type-item.active {
  background-color: #3498db;
  color: #ffffff;
}

/* 异常描述 */
.description-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  font-size: 26rpx;
  border: 1rpx solid #dddddd;
  border-radius: 10rpx;
  box-sizing: border-box;
}

/* 图片上传 */
.upload-grid {
  display: flex;
  gap: 20rpx;
}

.upload-item {
  position: relative;
  width: 150rpx;
  height: 150rpx;
  border: 1rpx solid #dddddd;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.remove-image {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.add-more {
  border: 2rpx dashed #dddddd;
}

.upload-icon {
  font-size: 48rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999999;
}

/* 联系方式 */
.contact-input {
  width: 100%;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  border: 1rpx solid #dddddd;
  border-radius: 10rpx;
  box-sizing: border-box;
}

/* 提交按钮 */
.submit-section {
  padding: 20rpx 30rpx;
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  background-color: #3498db;
  color: #ffffff;
  font-size: 30rpx;
  border-radius: 10rpx;
  border: none;
}
</style>