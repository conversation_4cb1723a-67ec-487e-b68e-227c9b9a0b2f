// 任务模块通用方法
export function filterTasks(tasks, filterType) {
  switch(filterType) {
    case 'pending':
      return tasks.filter(task => task.status === 'pending');
    case 'pickup':
      return tasks.filter(task => task.status === 'pickup');
    case 'delivery':
      return tasks.filter(task => task.status === 'delivery');
    default:
      return tasks;
  }
}

export function navigateToTaskDetail(taskId) {
  uni.navigateTo({
    url: `/pages/task/detail?id=${taskId}`
  });
}