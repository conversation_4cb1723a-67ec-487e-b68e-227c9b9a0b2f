<template>
  <view class="task-detail-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <text class="header-title">任务详情</text>
      <view class="header-right">
        <text class="more-icon">⋯</text>
      </view>
    </view>
    
    <!-- 任务状态 -->
    <view class="task-status">
      <view class="status-badge pending">{{ task.statusText }}</view>
      <view class="status-info">
        <text class="task-id">任务单号: {{ task.id }}</text>
        <text class="task-time">预约时间: {{ task.pickupTime }}</text>
      </view>
    </view>
    
    <!-- 客户信息 -->
    <view class="customer-info">
      <view class="section-title">客户信息</view>
      <view class="info-item">
        <text class="label">客户姓名:</text>
        <text class="value">{{ task.senderName }}</text>
      </view>
      <view class="info-item">
        <text class="label">联系电话:</text>
        <text class="value phone-number">{{ task.senderPhone }}</text>
      </view>
      <view class="info-item">
        <text class="label">取件地址:</text>
        <text class="value">{{ task.pickupAddress }}</text>
      </view>
    </view>
    
    <!-- 物品信息 -->
    <view class="package-info">
      <view class="section-title">物品信息</view>
      <view class="info-item">
        <text class="label">物品类型:</text>
        <text class="value">{{ task.packageType }}</text>
      </view>
      <view class="info-item">
        <text class="label">物品重量:</text>
        <text class="value">{{ task.packageWeight }}</text>
      </view>
      <view class="info-item">
        <text class="label">物品数量:</text>
        <text class="value">1件</text>
      </view>
      <view class="info-item">
        <text class="label">备注信息:</text>
        <text class="value">{{ task.remark }}</text>
      </view>
    </view>
    
    <!-- 费用信息 -->
    <view class="fee-info">
      <view class="section-title">费用信息</view>
      <view class="fee-item">
        <text class="label">运费:</text>
        <text class="value">¥8.00</text>
      </view>
      <view class="fee-item">
        <text class="label">保价费:</text>
        <text class="value">¥2.00</text>
      </view>
      <view class="fee-item total">
        <text class="label">合计:</text>
        <text class="value total-value">¥10.00</text>
      </view>
    </view>
    
    <!-- 操作记录 -->
    <view class="operation-records">
      <view class="section-title">操作记录</view>
      <view class="record-item">
        <text class="record-time">2023-04-01 09:30</text>
        <text class="record-desc">订单已创建</text>
      </view>
      <view class="record-item">
        <text class="record-time">2023-04-01 09:35</text>
        <text class="record-desc">快递员已接单</text>
      </view>
      <view class="record-item">
        <text class="record-time">2023-04-01 09:50</text>
        <text class="record-desc">正在前往取件点</text>
      </view>
    </view>
    
    <!-- 底部操作 -->
    <view class="bottom-actions">
      <button class="action-btn call-btn" @click="contactCustomer">联系客户</button>
      <button class="action-btn primary-btn" @click="startTask">去取件</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      task: {
        id: 'T20230401001',
        status: 'pickup', // pending, pickup, delivery, completed
        statusText: '待取件',
        statusClass: 'status-pickup',
        statusProgress: 2,
        trackingNumber: 'SF1234567890',
        courierCompany: '顺丰速运',
        packageType: '文件',
        packageWeight: '0.5kg',
        remark: '请轻拿轻放，谢谢！',
        customerName: '张先生',
        customerPhone: '138****8888',
        deliveryAddress: '北京市朝阳区某某街道某某小区1号楼101室',
        deliveryTime: '工作日 anytime',
        senderName: '李女士',
        senderPhone: '139****9999',
        pickupAddress: '北京市海淀区某某大厦202室',
        pickupTime: '2023-04-01 10:00-12:00'
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack({
        animationType: 'slide-out-right',
        animationDuration: 300
      });
    },
    startTask() {
      uni.showModal({
        title: '提示',
        content: '确认开始处理该任务？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '任务开始处理',
              icon: 'success'
            });
            // 更新任务状态
            this.task.status = 'pickup';
            this.task.statusText = '待取件';
            this.task.statusClass = 'status-pickup';
            this.task.statusProgress = 2;
          }
        }
      });
    },
    contactCustomer() {
      uni.showActionSheet({
        itemList: ['拨打电话', '发送短信'],
        success: (res) => {
          if (res.tapIndex === 0) {
            uni.makePhoneCall({
              phoneNumber: this.task.senderPhone
            });
          } else {
            uni.showToast({
              title: '短信功能开发中',
              icon: 'none'
            });
          }
        }
      });
    }
  }
}
</script>

<style scoped>
/* 容器 */
.task-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #3498db;
  color: white;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 28rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.more-icon {
  font-size: 36rpx;
}

/* 任务状态 */
.task-status {
  background-color: white;
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  font-size: 26rpx;
  color: white;
  margin-right: 30rpx;
}

.status-badge.pending {
  background-color: #f39c12;
}

.status-info {
  display: flex;
  flex-direction: column;
}

.task-id,
.task-time {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.task-time:last-child {
  margin-bottom: 0;
}

/* 内容区域 */
.customer-info,
.package-info,
.fee-info,
.operation-records {
  background-color: white;
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.info-item,
.fee-item,
.record-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-item:last-child,
.fee-item:last-child,
.record-item:last-child {
  margin-bottom: 0;
}

.label,
.record-time {
  font-size: 26rpx;
  color: #666;
}

.value,
.record-desc {
  font-size: 26rpx;
  color: #333;
  text-align: right;
}

.fee-item.total {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
  margin-top: 20rpx;
  font-weight: bold;
}

.total-value {
  color: #e74c3c;
  font-size: 32rpx;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 10rpx;
  border: none;
}

.call-btn {
  background-color: #f5f5f5;
  color: #333;
}

.primary-btn {
  background-color: #3498db;
  color: white;
}
</style>