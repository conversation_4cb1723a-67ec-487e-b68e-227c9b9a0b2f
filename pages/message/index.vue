<template>
  <view class="message-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <text class="header-title">消息中心</text>
      <view class="header-right">
        <text class="mark-all-read" @click="markAllRead">全部已读</text>
      </view>
    </view>
    
    <!-- 消息分类标签 -->
    <view class="message-tabs">
      <view 
        class="tab-item" 
        :class="{ active: currentTab === 'notice' }" 
        @click="switchTab('notice')">
        公告
      </view>
      <view 
        class="tab-item" 
        :class="{ active: currentTab === 'system' }" 
        @click="switchTab('system')">
        系统通知
      </view>
    </view>
    
    <!-- 系统通知子分类 -->
    <view class="sub-tabs" v-if="currentTab === 'system'">
      <view 
        class="sub-tab-item" 
        :class="{ active: currentSubTab === 'pickup' }" 
        @click="switchSubTab('pickup')">
        取件相关
      </view>
      <view 
        class="sub-tab-item" 
        :class="{ active: currentSubTab === 'delivery' }" 
        @click="switchSubTab('delivery')">
        派件相关
      </view>
      <view 
        class="sub-tab-item" 
        :class="{ active: currentSubTab === 'sign' }" 
        @click="switchSubTab('sign')">
        签收提醒
      </view>
      <view 
        class="sub-tab-item" 
        :class="{ active: currentSubTab === 'cancel' }" 
        @click="switchSubTab('cancel')">
        快件取消
      </view>
    </view>
    
    <!-- 消息列表 -->
    <scroll-view class="message-list" scroll-y="true">
      <view class="message-item" v-for="message in filteredMessages" :key="message.id" @click="readMessage(message)">
        <view class="message-header">
          <text class="message-title">{{ message.title }}</text>
          <text class="message-time">{{ message.time }}</text>
        </view>
        <view class="message-content">
          <text class="message-text">{{ message.content }}</text>
        </view>
        <view class="message-footer">
          <text class="message-type" :class="message.typeClass">{{ message.type }}</text>
          <text class="unread-dot" v-if="!message.read"></text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredMessages.length === 0">
        <text class="empty-text">暂无消息</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 'system', // notice: 公告, system: 系统通知
      currentSubTab: 'pickup', // pickup, delivery, sign, cancel
      messages: [
        {
          id: '1',
          title: '新的取件订单',
          content: '您有一个新的取件订单，请及时处理',
          time: '2023-04-01 10:30',
          type: '取件相关',
          typeClass: 'pickup',
          read: false
        },
        {
          id: '2',
          title: '订单即将超时',
          content: '订单SF1234567890即将超时，请尽快派送',
          time: '2023-04-01 14:20',
          type: '派件相关',
          typeClass: 'delivery',
          read: false
        },
        {
          id: '3',
          title: '客户催促派送',
          content: '客户张女士催促派送订单SF0987654321',
          time: '2023-04-01 15:45',
          type: '签收提醒',
          typeClass: 'sign',
          read: true
        },
        {
          id: '4',
          title: '系统维护通知',
          content: '系统将于今晚00:00-02:00进行维护，届时可能影响使用',
          time: '2023-04-01 09:00',
          type: '公告',
          typeClass: 'notice',
          read: true
        },
        {
          id: '5',
          title: '订单已取消',
          content: '客户李女士取消了订单SF1111111111',
          time: '2023-04-01 08:15',
          type: '快件取消',
          typeClass: 'cancel',
          read: true
        }
      ]
    }
  },
  computed: {
    filteredMessages() {
      return this.messages.filter(message => {
        if (this.currentTab === 'notice') {
          return message.typeClass === 'notice';
        } else {
          // 系统通知
          if (this.currentSubTab === 'pickup') {
            return message.typeClass === 'pickup';
          } else if (this.currentSubTab === 'delivery') {
            return message.typeClass === 'delivery';
          } else if (this.currentSubTab === 'sign') {
            return message.typeClass === 'sign';
          } else if (this.currentSubTab === 'cancel') {
            return message.typeClass === 'cancel';
          }
        }
        return true;
      });
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    switchTab(tab) {
      this.currentTab = tab;
    },
    switchSubTab(subTab) {
      this.currentSubTab = subTab;
    },
    readMessage(message) {
      // 标记消息为已读
      message.read = true;
      uni.showToast({
        title: '查看消息',
        icon: 'none'
      });
    },
    markAllRead() {
      this.messages.forEach(message => {
        message.read = true;
      });
      uni.showToast({
        title: '全部标记为已读',
        icon: 'success'
      });
    }
  }
}
</script>

<style scoped>
/* 容器 */
.message-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  padding: 10rpx;
}

.back-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
  color: var(--text-white);
}

.back-text {
  font-size: 28rpx;
  color: var(--text-white);
}

.header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 36rpx;
  font-weight: bold;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.mark-all-read {
  font-size: 28rpx;
}

/* 消息分类标签 */
.message-tabs {
  display: flex;
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-color);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: var(--text-gray);
}

.tab-item.active {
  color: var(--primary-color);
  border-bottom: 4rpx solid var(--primary-color);
}

/* 系统通知子分类 */
.sub-tabs {
  display: flex;
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-color);
  padding: 0 20rpx;
}

.sub-tab-item {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  font-size: 24rpx;
  color: var(--text-gray);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sub-tab-item.active {
  color: var(--primary-color);
  border-bottom: 4rpx solid var(--primary-color);
}

/* 消息列表 */
.message-list {
  flex: 1;
}

.message-item {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.message-title {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.message-time {
  font-size: 24rpx;
  color: var(--text-gray);
}

.message-content {
  margin-bottom: 10rpx;
}

.message-text {
  font-size: 26rpx;
  color: var(--text-gray);
  line-height: 1.5;
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-type {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  color: var(--text-white);
}

.message-type.pickup {
  background-color: var(--accent-color);
}

.message-type.delivery {
  background-color: #3498db;
}

.message-type.sign {
  background-color: #27ae60;
}

.message-type.cancel {
  background-color: #95a5a6;
}

.message-type.notice {
  background-color: #f39c12;
}

.unread-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-gray);
}
</style>