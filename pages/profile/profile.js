// 个人资料模块通用方法
export function fetchUserProfile() {
  return new Promise((resolve, reject) => {
    uni.request({
      url: '/api/profile',
      method: 'GET',
      header: {
        Authorization: `Bearer ${uni.getStorageSync('token')}`
      },
      success(res) {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res.data.message || '获取用户信息失败');
        }
      }
    });
  });
}

export function updateProfile(data) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: '/api/profile',
      method: 'PUT',
      header: {
        Authorization: `Bearer ${uni.getStorageSync('token')}`
      },
      data,
      success(res) {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res.data.message || '更新用户信息失败');
        }
      }
    });
  });
}