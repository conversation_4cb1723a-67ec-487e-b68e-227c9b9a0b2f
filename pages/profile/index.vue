<template>
  <view class="profile-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <text class="header-title">个人中心</text>
    </view>
    
    <!-- 用户信息 -->
    <view class="profile-section">
      <view class="user-info">
        <image class="avatar" src="/static/logo.png" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name">{{ userInfo.name }}</text>
          <text class="user-id">工号: {{ userInfo.employeeId }}</text>
          <text class="user-phone">{{ userInfo.phone }}</text>
        </view>
        <view class="edit-btn" @click="editProfile">
          <text class="edit-icon">✏️</text>
        </view>
      </view>
      
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{ userInfo.rating }}</text>
          <text class="stat-label">评分</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ userInfo.completedTasks }}</text>
          <text class="stat-label">完成任务</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ userInfo.workDays }}</text>
          <text class="stat-label">工作天数</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="goToPage('/pages/profile/settings')">
        <text class="menu-icon">⚙️</text>
        <text class="menu-text">系统设置</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goToPage('/pages/profile/wallet')">
        <text class="menu-icon">💰</text>
        <text class="menu-text">我的钱包</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goToPage('/pages/profile/orders')">
        <text class="menu-icon">📦</text>
        <text class="menu-text">历史订单</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goToPage('/pages/profile/certificates')">
        <text class="menu-icon">📄</text>
        <text class="menu-text">证件管理</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goToPage('/pages/profile/help')">
        <text class="menu-icon">❓</text>
        <text class="menu-text">帮助中心</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goToPage('/pages/profile/about')">
        <text class="menu-icon">ℹ️</text>
        <text class="menu-text">关于我们</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="logout">退出登录</button>
    </view>
    
    <!-- 底部导航栏 -->
    <view class="tab-bar">
      <view class="tab-item" @click="goToIndex">
        <text class="tab-icon">🏠</text>
        <text class="tab-label">首页</text>
      </view>
      <view class="tab-item" @click="goToPickup">
        <text class="tab-icon">📦</text>
        <text class="tab-label">取件</text>
      </view>
      <view class="tab-item scan-center">
        <text class="tab-icon">📷</text>
        <text class="tab-label">扫码</text>
      </view>
      <view class="tab-item" @click="goToDelivery">
        <text class="tab-icon">🚚</text>
        <text class="tab-label">派件</text>
      </view>
      <view class="tab-item active">
        <text class="tab-icon">👤</text>
        <text class="tab-label">我的</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        name: '张三',
        employeeId: 'E2023001',
        phone: '138****8888',
        rating: '4.8',
        completedTasks: 1256,
        workDays: 320
      }
    }
  },
  methods: {
    editProfile() {
      uni.showToast({
        title: '编辑资料功能开发中',
        icon: 'none'
      });
    },
    goToPage(url) {
      uni.navigateTo({
        url: url,
        animationType: 'slide-in-right',
        animationDuration: 300
      });
    },
    logout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出当前账号吗？',
        success: (res) => {
          if (res.confirm) {
            uni.redirectTo({
              url: '/pages/login/login'
            });
          }
        }
      });
    },
    goToIndex() {
      uni.switchTab({
        url: '/pages/main/index'
      });
    },
    goToPickup() {
      uni.switchTab({
        url: '/pages/task/list'
      });
    },
    goToDelivery() {
      uni.navigateTo({
        url: '/pages/task/list?taskType=delivery'
      });
    },
    scanCode() {
      uni.scanCode({
        success: (res) => {
          uni.showToast({
            title: '扫码成功: ' + res.result,
            icon: 'none'
          });
        },
        fail: () => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style scoped>
.profile-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-top: max(env(safe-area-inset-top), 40rpx);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

.status-bar {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: var(--primary-color);
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.profile-section {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: var(--bg-gray);
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.user-id, .user-phone {
  font-size: 26rpx;
  color: var(--text-gray);
}

.edit-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: var(--bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  font-size: 30rpx;
}

.stats-grid {
  display: flex;
  border-top: 1rpx solid var(--border-color);
  padding-top: 30rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-item:not(:last-child) {
  border-right: 1rpx solid var(--border-color);
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 26rpx;
  color: var(--text-gray);
}

.menu-section {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  width: 50rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-dark);
}

.menu-arrow {
  color: var(--text-gray);
  font-size: 36rpx;
}

.logout-section {
  margin: 20rpx 30rpx;
}

.logout-btn {
  width: 100%;
  padding: 20rpx;
  background-color: #e74c3c;
  color: var(--text-white);
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
  font-weight: bold;
}
/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: var(--bg-white);
  border-top: 1rpx solid var(--border-color);
  display: flex;
  padding: 10rpx 0 env(safe-area-inset-bottom);
  z-index: 999;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: var(--text-gray);
}

.tab-item.active {
  color: var(--primary-color);
}

.tab-icon {
  font-size: 36rpx;
  margin-bottom: 6rpx;
}

.scan-center {
  flex: 1.5;
  background-color: var(--primary-color);
  border-radius: 50rpx;
  margin: 0 20rpx;
  color: var(--text-white);
}
</style>