/*
 * 登录页面样式文件
 * 定义登录页面使用的所有CSS样式
 */

/* 全局样式 - 防止页面滚动 */
html, body {
  height: 100%;
  overflow: hidden;
  background-color: var(--bg-light);
}

:root {
  /* 主题颜色 */
  --primary-color: #3498db;      /* 主色调 - 现代蓝色 */
  --secondary-color: #2c3e50;    /* 辅助色调 - 深蓝灰色 */
  --accent-color: #1abc9c;       /* 强调色 - 青绿色 */
  --primary-hover: #2980b9;      /* 主色调悬停状态 */

  /* 背景颜色 */
  --bg-white: #ffffff;           /* 白色背景 */
  --bg-light: #f8f9fa;           /* 浅色半透明背景 */
  --bg-gray: #bdc3c7;            /* 灰色背景 - 禁用状态按钮 */
  --bg-dark: #ecf0f1;            /* 深灰色背景 */

  /* 文本颜色 */
  --text-white: #ffffff;         /* 白色文本 */
  --text-gray: #7f8c8d;          /* 灰色文本 */
  --text-dark: #2c3e50;          /* 深色文本 */
  --text-error: #e74c3c;         /* 错误文本颜色 */

  /* 边框颜色 */
  --border-color: #dcdde1;       /* 输入框边框颜色 */
  --border-focus: #3498db;       /* 聚焦边框颜色 */

  /* 阴影颜色 */
  --shadow-color: rgba(0, 0, 0, 0.1);     /* 普通阴影 */
  --shadow-light: rgba(0, 0, 0, 0.1);     /* 浅色阴影 */
  --shadow-dark: rgba(0, 0, 0, 0.2);      /* 深色阴影 */
  --shadow-primary: rgba(52, 152, 219, 0.3); /* 主题色阴影 */
  --shadow-accent: rgba(26, 188, 156, 0.3);  /* 强调色阴影 */
}

/* 登录容器 - 优化响应式布局 */
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 40rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  /* 使用安全区域适配，避免与状态栏重叠 */
  padding-top: max(env(safe-area-inset-top), 40rpx);
  padding-bottom: max(env(safe-area-inset-bottom), 40rpx);
  transition: all 0.3s ease;
}

/* 登录模块通用样式 */
.login-container {
  padding-top: max(env(safe-area-inset-top), 40rpx);
  min-height: 100vh;
  background-color: rgba(255, 255, 255, 0.9);
}

.logo {
  margin-top: 60rpx;
  text-align: center;
}

.form-group {
  padding: 0 40rpx;
  margin-bottom: 30rpx;
}

.input-field {
  height: 80rpx;
  line-height: 80rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  padding: 0 20rpx;
}

/* 状态栏样式 */
.status-bar {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: transparent;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

/* 登录容器装饰元素 */
.login-container::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  transform: rotate(30deg);
  z-index: 0;
}

/* 登录头部 */
.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
  transform: translateY(-40rpx);
}

/* Logo图标 */
.logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 30rpx;
  box-shadow: 0 10rpx 30rpx var(--shadow-dark);
  margin-bottom: 30rpx;
  margin-top: 10rpx;
  background-color: var(--bg-white);
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 80rpx;
  font-weight: bold;
}

/* 应用标题 */
.app-title {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--text-white);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 2rpx;
}

/* 登录标签页容器 */
.login-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50rpx;
  padding: 5rpx;
  max-width: 500rpx;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* 标签项 */
.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 32rpx;
  color: var(--text-white);
  border-radius: 50rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  font-weight: 500;
}

/* 激活的标签项 */
.tab-item.active {
  background-color: var(--bg-white);
  color: var(--primary-color);
  font-weight: 700;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 登录表单 - 优化响应式布局 */
.login-form {
  width: 100%;
  max-width: 600rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  margin-top: 10rpx;
  margin-bottom: 40rpx;
  transition: all 0.3s ease;
}

/* 输入框组 */
.input-group {
  position: relative;
  margin-bottom: 30rpx;
}

/* 登录输入框 */
.login-input {
  width: 100%;
  height: 80rpx;
  padding: 0 30rpx;
  border: 2rpx solid var(--border-color);
  border-radius: 15rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: var(--bg-white);
  color: var(--text-dark);
  transition: all 0.3s ease;
}

/* 输入框聚焦状态 */
.login-input:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 3rpx var(--shadow-primary);
  outline: none;
}

/* 密码组容器 */
.password-group {
  display: flex;
  align-items: center;
}

/* 密码切换按钮 */
.password-toggle {
  position: absolute;
  right: 25rpx;
  color: var(--primary-color);
  font-size: 26rpx;
  z-index: 2;
  background-color: var(--bg-white);
  padding: 0 10rpx;
  cursor: pointer;
}

/* 眼睛图标容器 */
.eye-icon {
  position: absolute;
  right: 25rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  background-color: #FFFFFF;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
}

/* 眼睛图标样式 */
.eye-icon::before {
  content: "";
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid var(--primary-color);
  border-radius: 50%;
  position: relative;
}

/* 眼睛瞳孔 */
.eye-icon::after {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 8rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 闭眼图标样式（斜线） */
.eye-icon.closed::before {
  content: "";
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid var(--primary-color);
  border-radius: 50%;
  position: relative;
}
.eye-icon.closed::after {
  content: "";
  display: block;
  width: 2rpx;
  height: 30rpx;
  background-color: var(--primary-color);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  border-radius: 0;
}

/* 验证码组容器 */
.verification-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20rpx;
}

/* 验证码输入框 */
.verification-input {
  flex: 1;
}

/* 验证码按钮 */
.verification-button {
  width: 200rpx;
  height: 80rpx;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  color: var(--text-white);
  border: none;
  border-radius: 15rpx;
  font-size: 26rpx;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  padding: 0;
  box-sizing: border-box;
  font-weight: 500;
  box-shadow: 0 4rpx 10rpx var(--shadow-primary);
  transition: all 0.3s ease;
}

/* 验证码按钮悬停状态 */
.verification-button:hover:not(:disabled) {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 15rpx var(--shadow-primary);
}

/* 验证码按钮禁用状态 */
.verification-button:disabled {
  background: linear-gradient(to right, var(--bg-gray), #95a5a6);
  cursor: not-allowed;
  box-shadow: none;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  color: var(--text-white);
  border: none;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: 700;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 15rpx var(--shadow-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 2rpx;
  box-sizing: border-box;
}

/* 登录按钮悬停状态 */
.login-button:hover:not(:disabled) {
  transform: translateY(-3rpx);
  box-shadow: 0 7rpx 20rpx var(--shadow-dark);
}

/* 登录按钮禁用状态 */
.login-button:disabled {
  background: linear-gradient(to right, var(--bg-gray), #95a5a6);
  cursor: not-allowed;
  box-shadow: none;
}

/* 错误信息 */
.error-message {
  color: var(--text-error);
  font-size: 24rpx;
  margin-top: 20rpx;
  text-align: center;
  font-weight: 500;
}

/* ===== 响应式媒体查询 ===== */

/* 平板设备（中等屏幕 - 介于手机和大屏幕之间） */
@media screen and (min-width: 600px) and (max-width: 767px) {
  /* 调整Logo大小 */
  .logo {
    width: 170rpx;
    height: 170rpx;
  }
  
  /* 调整应用标题大小 */
  .app-title {
    font-size: 52rpx;
  }
  
  /* 调整登录表单 */
  .login-form {
    padding: 45rpx;
    max-width: 650rpx;
  }
  
  /* 调整输入框高度 */
  .login-input {
    height: 85rpx;
    font-size: 30rpx;
  }
  
  /* 调整登录按钮高度 */
  .login-button {
    height: 85rpx;
    font-size: 34rpx;
  }
  
  /* 调整验证码按钮大小 */
  .verification-button {
    width: 210rpx;
    height: 85rpx;
    font-size: 26rpx;
  }
  
  /* 调整登录标签字体大小 */
  .tab-item {
    font-size: 34rpx;
    padding: 22rpx 0;
  }
}

/* 小屏幕设备（手机横屏或小型平板） */
@media screen and (max-width: 375px) {
  /* 调整Logo大小 */
  .logo {
    width: 140rpx;
    height: 140rpx;
  }
  
  /* 调整应用标题大小 */
  .app-title {
    font-size: 42rpx;
  }
  
  /* 调整登录表单内边距 */
  .login-form {
    padding: 30rpx;
    transform: scale(0.95);
  }
  
  /* 调整输入框高度 */
  .login-input {
    height: 70rpx;
    font-size: 26rpx;
  }
  
  /* 调整登录按钮高度 */
  .login-button {
    height: 70rpx;
    font-size: 28rpx;
  }
  
  /* 调整验证码按钮大小 */
  .verification-button {
    width: 180rpx;
    height: 70rpx;
    font-size: 24rpx;
  }
  
  /* 调整登录标签字体大小 */
  .tab-item {
    font-size: 28rpx;
    padding: 15rpx 0;
  }
  
  /* 表单元素间距 */
  .input-group {
    margin-bottom: 25rpx;
  }
  
  /* 调整登录容器布局 */
  .login-container {
    justify-content: center;
  }
}

/* 大屏幕设备（平板及以上） */
@media screen and (min-width: 768px) {
  /* 调整整体布局 */
  .login-container {
    justify-content: center;
  }
  
  /* 添加卡片式效果 */
  .login-form {
    transform: translateY(0);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .login-form:hover {
    transform: translateY(-10rpx);
    box-shadow: 0 20rpx 45rpx rgba(0, 0, 0, 0.25);
  }
  /* 调整Logo大小 */
  .logo {
    width: 180rpx;
    height: 180rpx;
  }
  
  /* 调整应用标题大小 */
  .app-title {
    font-size: 56rpx;
  }
  
  /* 调整登录表单内边距 */
  .login-form {
    padding: 50rpx;
    max-width: 700rpx;
  }
  
  /* 调整输入框高度 */
  .login-input {
    height: 90rpx;
    font-size: 32rpx;
  }
  
  /* 调整登录按钮高度 */
  .login-button {
    height: 90rpx;
    font-size: 36rpx;
  }
  
  /* 调整验证码按钮大小 */
  .verification-button {
    width: 220rpx;
    height: 90rpx;
    font-size: 28rpx;
  }
  
  /* 调整登录标签字体大小 */
  .tab-item {
    font-size: 36rpx;
    padding: 25rpx 0;
  }
}

/* 超小屏幕设备 */
@media screen and (max-width: 320px) {
  /* 整体缩放适配 */
  .login-container {
    padding: 0 15rpx;
  }
  
  /* 表单进一步缩小 */
  .login-form {
    padding: 25rpx;
    transform: scale(0.92);
  }
  
  /* 紧凑布局 */
  .logo {
    margin-top: 5rpx;
    margin-bottom: 20rpx;
  }
  
  /* 减少元素间距 */
  .login-header {
    margin-bottom: 15rpx;
  }
  
  .login-tabs {
    margin-bottom: 15rpx;
  }
  
  /* 增大触摸目标 */
  .login-button,
  .verification-button,
  .eye-icon {
    min-height: 60rpx;
  }
  /* 调整登录容器内边距 */
  .login-container {
    padding: 0 20rpx;
  }
}