<template>
  <view class="statistics-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <text class="header-title">工作统计</text>
      <view class="header-actions">
        <text class="date-selector" @click="selectDateRange">📅</text>
      </view>
    </view>
    
    <!-- 统计概览 -->
    <view class="overview-section">
      <view class="overview-item">
        <text class="item-value">{{ statistics.totalTasks }}</text>
        <text class="item-label">总任务数</text>
      </view>
      <view class="overview-item">
        <text class="item-value">{{ statistics.completedTasks }}</text>
        <text class="item-label">完成任务</text>
      </view>
      <view class="overview-item">
        <text class="item-value">{{ statistics.income }}</text>
        <text class="item-label">收入(元)</text>
      </view>
    </view>
    
    <!-- 任务统计图表 -->
    <view class="chart-section">
      <view class="section-header">
        <text class="section-title">任务完成趋势</text>
      </view>
      <view class="chart-container">
        <!-- 简化的柱状图 -->
        <view class="chart">
          <view class="chart-y-axis">
            <text class="y-label">30</text>
            <text class="y-label">20</text>
            <text class="y-label">10</text>
            <text class="y-label">0</text>
          </view>
          <view class="chart-content">
            <view class="chart-bars">
              <view class="bar-item" v-for="(item, index) in chartData" :key="index">
                <view class="bar" :style="{ height: item.height }">
                  <text class="bar-value">{{ item.value }}</text>
                </view>
                <text class="bar-label">{{ item.label }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 详细统计 -->
    <view class="detail-section">
      <view class="section-header">
        <text class="section-title">详细统计</text>
      </view>
      <view class="detail-list">
        <view class="detail-item">
          <text class="detail-label">待取件</text>
          <text class="detail-value">{{ statistics.pendingPickup }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">待派送</text>
          <text class="detail-value">{{ statistics.pendingDelivery }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">今日完成</text>
          <text class="detail-value">{{ statistics.todayCompleted }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">异常任务</text>
          <text class="detail-value">{{ statistics.exceptionTasks }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">平均完成时间</text>
          <text class="detail-value">{{ statistics.avgCompletionTime }}分钟</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statistics: {
        totalTasks: 128,
        completedTasks: 115,
        income: 860,
        pendingPickup: 3,
        pendingDelivery: 5,
        todayCompleted: 28,
        exceptionTasks: 2,
        avgCompletionTime: 45
      },
      chartData: [
        { label: '周一', value: 18, height: '60%' },
        { label: '周二', value: 22, height: '73%' },
        { label: '周三', value: 15, height: '50%' },
        { label: '周四', value: 25, height: '83%' },
        { label: '周五', value: 30, height: '100%' },
        { label: '周六', value: 12, height: '40%' },
        { label: '周日', value: 8, height: '27%' }
      ]
    }
  },
  methods: {
    selectDateRange() {
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    goToPage(url) {
      uni.switchTab({
        url: `/${url}`,
        animationType: 'slide-in-right',
        animationDuration: 300
      });
    }
  }
}
</script>

<style scoped>
.statistics-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: max(env(safe-area-inset-top), 40rpx);
  padding-bottom: 120rpx;
  box-sizing: border-box;
}

.status-bar {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: #3498db;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #3498db;
  color: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.date-selector {
  font-size: 36rpx;
}

.overview-section {
  display: flex;
  background-color: white;
  margin: 20rpx;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-item:not(:last-child) {
  border-right: 1rpx solid #eee;
}

.item-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 10rpx;
}

.item-label {
  font-size: 26rpx;
  color: #666;
}

.chart-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.chart-container {
  height: 300rpx;
}

.chart {
  display: flex;
  height: 100%;
}

.chart-y-axis {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 60rpx;
  padding-right: 20rpx;
  border-right: 1rpx solid #eee;
}

.y-label {
  font-size: 20rpx;
  color: #999;
}

.chart-content {
  flex: 1;
  padding-left: 20rpx;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 100%;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.bar {
  width: 40rpx;
  background: linear-gradient(to top, #3498db, #2980b9);
  border-radius: 10rpx 10rpx 0 0;
  margin-bottom: 10rpx;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 10rpx;
}

.bar-value {
  font-size: 20rpx;
  color: white;
}

.bar-label {
  font-size: 20rpx;
  color: #666;
}

.detail-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.detail-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: white;
  display: flex;
  border-top: 1rpx solid #eee;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.tab-item.active {
  color: #3498db;
}

.tab-icon {
  font-size: 40rpx;
  margin-bottom: 5rpx;
}
</style>