// 数据统计模块通用方法
export function fetchStatistics() {
  return new Promise((resolve, reject) => {
    uni.request({
      url: '/api/statistics',
      method: 'GET',
      header: {
        Authorization: `Bearer ${uni.getStorageSync('token')}`
      },
      success(res) {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res.data.message || '获取统计数据失败');
        }
      }
    });
  });
}

export function initCharts(chartData) {
  const chart = new Chart('#statsChart', {
    type: 'line',
    data: chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false
    }
  });
}