<template>
  <view class="exclusive-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <text class="header-title">专属取寄</text>
      <view class="header-right"></view>
    </view>
    
    <!-- 功能介绍 -->
    <view class="intro-section">
      <view class="intro-title">专属取寄服务</view>
      <view class="intro-content">
        <text class="intro-text">为您的客户提供专属的快递取寄服务，享受更优质的物流体验</text>
      </view>
    </view>
    
    <!-- 服务类型 -->
    <view class="service-types">
      <view class="section-title">服务类型</view>
      <view class="type-list">
        <view 
          class="type-item" 
          v-for="service in serviceTypes" 
          :key="service.value"
          :class="{ active: selectedService === service.value }"
          @click="selectService(service.value)"
        >
          <text class="type-icon">{{ service.icon }}</text>
          <text class="type-name">{{ service.name }}</text>
        </view>
      </view>
    </view>
    
    <!-- 客户信息 -->
    <view class="customer-info">
      <view class="section-title">客户信息</view>
      <view class="form-group">
        <text class="form-label">客户姓名</text>
        <input 
          class="form-input" 
          type="text" 
          v-model="customerInfo.name" 
          placeholder="请输入客户姓名">
      </view>
      <view class="form-group">
        <text class="form-label">联系电话</text>
        <input 
          class="form-input" 
          type="number" 
          v-model="customerInfo.phone" 
          placeholder="请输入联系电话">
      </view>
    </view>
    
    <!-- 取寄信息 -->
    <view class="pickup-delivery-info">
      <view class="section-title">取寄信息</view>
      <view class="form-group">
        <text class="form-label">取件地址</text>
        <textarea 
          class="form-textarea" 
          v-model="pickupInfo.address" 
          placeholder="请输入取件地址"></textarea>
        <button class="location-btn" @click="getLocation">获取当前位置</button>
      </view>
      <view class="form-group">
        <text class="form-label">派送地址</text>
        <textarea 
          class="form-textarea" 
          v-model="deliveryInfo.address" 
          placeholder="请输入派送地址"></textarea>
      </view>
      <view class="form-row">
        <view class="form-group half">
          <text class="form-label">预约时间</text>
          <input 
            class="form-input" 
            type="text" 
            v-model="appointment.time" 
            placeholder="请选择预约时间"
            @click="selectTime">
        </view>
        <view class="form-group half">
          <text class="form-label">物品重量(kg)</text>
          <input 
            class="form-input" 
            type="digit" 
            v-model="packageInfo.weight" 
            placeholder="请输入物品重量">
        </view>
      </view>
    </view>
    
    <!-- 备注信息 -->
    <view class="remark-section">
      <view class="form-group">
        <text class="form-label">备注信息</text>
        <textarea 
          class="form-textarea" 
          v-model="remark" 
          placeholder="请输入备注信息，如特殊要求等"></textarea>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" @click="submitOrder">提交订单</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedService: 'pickup', // 默认选择取件服务
      serviceTypes: [
        { name: '取件服务', value: 'pickup', icon: '📦' },
        { name: '派送服务', value: 'delivery', icon: '🚚' },
        { name: '取派一体', value: 'both', icon: '🔁' }
      ],
      customerInfo: {
        name: '',
        phone: ''
      },
      pickupInfo: {
        address: ''
      },
      deliveryInfo: {
        address: ''
      },
      appointment: {
        time: ''
      },
      packageInfo: {
        weight: ''
      },
      remark: ''
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    selectService(service) {
      this.selectedService = service;
    },
    getLocation() {
      // 获取当前位置
      uni.showToast({
        title: '获取位置功能开发中',
        icon: 'none'
      });
    },
    selectTime() {
      // 选择预约时间
      uni.showToast({
        title: '选择时间功能开发中',
        icon: 'none'
      });
    },
    submitOrder() {
      // 验证必填信息
      if (!this.customerInfo.name) {
        uni.showToast({
          title: '请输入客户姓名',
          icon: 'none'
        });
        return;
      }
      
      if (!this.customerInfo.phone) {
        uni.showToast({
          title: '请输入联系电话',
          icon: 'none'
        });
        return;
      }
      
      if (!this.pickupInfo.address) {
        uni.showToast({
          title: '请输入取件地址',
          icon: 'none'
        });
        return;
      }
      
      if (!this.deliveryInfo.address) {
        uni.showToast({
          title: '请输入派送地址',
          icon: 'none'
        });
        return;
      }
      
      if (!this.appointment.time) {
        uni.showToast({
          title: '请选择预约时间',
          icon: 'none'
        });
        return;
      }
      
      // 提交订单
      uni.showModal({
        title: '提交订单',
        content: '确认提交专属取寄订单？',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '提交中...'
            });
            
            // 模拟提交过程
            setTimeout(() => {
              uni.hideLoading();
              uni.showToast({
                title: '提交成功',
                icon: 'success'
              });
              
              // 返回上一页
              setTimeout(() => {
                uni.navigateBack();
              }, 1000);
            }, 1500);
          }
        }
      });
    }
  }
}
</script>

<style scoped>
/* 容器 */
.exclusive-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 28rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.header-right {
  width: 80rpx;
}

/* 功能介绍 */
.intro-section {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
  text-align: center;
}

.intro-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 20rpx;
}

.intro-text {
  font-size: 26rpx;
  color: var(--text-gray);
  line-height: 1.5;
}

/* 服务类型 */
.service-types {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.type-list {
  display: flex;
  gap: 20rpx;
}

.type-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
}

.type-item.active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.type-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.type-name {
  font-size: 26rpx;
}

/* 客户信息和取寄信息 */
.customer-info,
.pickup-delivery-info,
.remark-section {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.form-group {
  margin-bottom: 30rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-row {
  display: flex;
  gap: 20rpx;
}

.half {
  flex: 1;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: var(--text-dark);
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 10rpx;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 120rpx;
  padding: 20rpx;
  font-size: 26rpx;
  border: 1rpx solid var(--border-color);
  border-radius: 10rpx;
  box-sizing: border-box;
}

.location-btn {
  width: 100%;
  height: 70rpx;
  background-color: var(--bg-light);
  color: var(--text-dark);
  font-size: 26rpx;
  border-radius: 10rpx;
  border: 1rpx solid var(--border-color);
  margin-top: 20rpx;
}

/* 提交按钮 */
.submit-section {
  padding: 20rpx 30rpx;
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
  font-size: 30rpx;
  border-radius: 10rpx;
  border: none;
}
</style>