<template>
  <view class="search-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <view class="search-box">
        <text class="search-icon">🔍</text>
        <input 
          class="search-input" 
          type="text" 
          v-model="searchKeyword" 
          placeholder="输入运单号/手机号/姓名关键词" 
          @confirm="performSearch"
        >
        <text class="clear-icon" v-if="searchKeyword" @click="clearKeyword">✕</text>
      </view>
      <text class="cancel-btn" @click="goBack">取消</text>
    </view>
    
    <!-- 搜索结果 -->
    <view class="search-results" v-if="searchResults.length > 0">
      <view class="result-item" v-for="result in searchResults" :key="result.id" @click="goToResultDetail(result)">
        <view class="result-header">
          <text class="result-title">{{ result.title }}</text>
          <text class="result-type" :class="result.typeClass">{{ result.type }}</text>
        </view>
        <view class="result-content">
          <text class="result-desc">{{ result.description }}</text>
        </view>
        <view class="result-meta">
          <text class="result-time">{{ result.time }}</text>
        </view>
      </view>
    </view>
    
    <!-- 最近搜索 -->
    <view class="recent-searches" v-else>
      <view class="section-header">
        <text class="section-title">最近搜索</text>
        <text class="clear-all" @click="clearAllHistory">清空历史</text>
      </view>
      <view class="history-list">
        <view class="history-item" v-for="(history, index) in searchHistory" :key="index" @click="searchFromHistory(history)">
          <text class="history-text">{{ history }}</text>
        </view>
      </view>
      
      <!-- 热门搜索 -->
      <view class="section-header">
        <text class="section-title">热门搜索</text>
      </view>
      <view class="hot-searches">
        <view class="hot-item" v-for="(hot, index) in hotSearches" :key="index" @click="searchFromHot(hot)">
          <text class="hot-text">{{ hot }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      searchHistory: ['SF1234567890', '张三', '13800138000'],
      hotSearches: ['今日任务', '超时订单', '紧急派送', '客户催单'],
      searchResults: []
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    clearKeyword() {
      this.searchKeyword = '';
      this.searchResults = [];
    },
    performSearch() {
      if (!this.searchKeyword.trim()) {
        uni.showToast({
          title: '请输入搜索关键词',
          icon: 'none'
        });
        return;
      }
      
      // 添加到搜索历史
      if (!this.searchHistory.includes(this.searchKeyword)) {
        this.searchHistory.unshift(this.searchKeyword);
        // 限制历史记录数量
        if (this.searchHistory.length > 10) {
          this.searchHistory.pop();
        }
      }
      
      // 模拟搜索结果
      this.searchResults = [
        {
          id: '1',
          title: '订单 SF1234567890',
          type: '取件任务',
          typeClass: 'pickup',
          description: '寄件人: 张三 138****8888 | 北京市朝阳区某某小区',
          time: '2023-04-01 10:30'
        },
        {
          id: '2',
          title: '订单 SF0987654321',
          type: '派件任务',
          typeClass: 'delivery',
          description: '收货人: 李四 139****9999 | 北京市海淀区某某大厦',
          time: '2023-04-01 14:20'
        }
      ];
    },
    clearAllHistory() {
      uni.showModal({
        title: '清空搜索历史',
        content: '确定要清空所有搜索历史记录吗？',
        success: (res) => {
          if (res.confirm) {
            this.searchHistory = [];
            uni.showToast({
              title: '已清空',
              icon: 'success'
            });
          }
        }
      });
    },
    searchFromHistory(keyword) {
      this.searchKeyword = keyword;
      this.performSearch();
    },
    searchFromHot(keyword) {
      this.searchKeyword = keyword;
      this.performSearch();
    },
    goToResultDetail(result) {
      // 跳转到搜索结果详情页
      console.log('跳转到结果详情页', result);
      uni.showToast({
        title: '跳转功能开发中',
        icon: 'none'
      });
    }
  }
}
</script>

<style scoped>
/* 容器 */
.search-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-color);
}

.header-left {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.back-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 28rpx;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
}

.search-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
  color: var(--text-gray);
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  padding: 10rpx 0;
  background: transparent;
  border: none;
  outline: none;
}

.clear-icon {
  font-size: 28rpx;
  color: var(--text-gray);
  padding: 10rpx;
}

.cancel-btn {
  font-size: 28rpx;
  color: var(--primary-color);
  margin-left: 20rpx;
  padding: 10rpx 0;
}

/* 搜索结果 */
.search-results {
  flex: 1;
  padding: 20rpx 30rpx;
}

.result-item {
  background-color: var(--bg-white);
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-title {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.result-type {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  color: var(--text-white);
}

.result-type.pickup {
  background-color: var(--accent-color);
}

.result-type.delivery {
  background-color: #3498db;
}

.result-content {
  margin-bottom: 10rpx;
}

.result-desc {
  font-size: 26rpx;
  color: var(--text-gray);
}

.result-meta {
  text-align: right;
}

.result-time {
  font-size: 24rpx;
  color: var(--text-gray);
}

/* 最近搜索 */
.recent-searches {
  flex: 1;
  padding: 20rpx 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 30rpx 0 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.clear-all {
  font-size: 26rpx;
  color: var(--primary-color);
}

.history-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.history-item {
  background-color: var(--bg-white);
  padding: 15rpx 25rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  color: var(--text-dark);
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.hot-searches {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.hot-item {
  background-color: var(--bg-white);
  padding: 15rpx 25rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  color: var(--primary-color);
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}
</style>