// 首页模块通用方法
export function fetchDashboardData() {
  return new Promise((resolve) => {
    // 模拟首页数据
    setTimeout(() => {
      resolve({
        success: true,
        message: '获取首页数据成功',
        data: {
          ordersCount: 120,
          pendingOrders: 20,
          completedOrders: 80,
          revenue: '¥12,345.67'
        }
      });
    }, 500);
  });
}

export function handleAction(actionType) {
  switch(actionType) {
    case 'pickup':
      uni.navigateTo({
        url: '/pages/task/pickup'
      });
      break;
    case 'delivery':
      uni.navigateTo({
        url: '/pages/task/delivery'
      });
      break;
    case 'profile':
      uni.navigateTo({
        url: '/pages/profile/index'
      });
      break;
    case 'statistics':
      uni.navigateTo({
        url: '/pages/statistics/index'
      });
      break;
  }
}