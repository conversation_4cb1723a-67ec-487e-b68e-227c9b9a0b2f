<template>
  <!-- 首页容器 -->
  <view class="home-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 顶部搜索栏 -->
    <view class="search-bar" @click="goToSearch">
      <text class="search-icon">🔍</text>
      <text class="search-placeholder">输入运单号/手机号/姓名关键词</text>
      <view class="search-actions">
        <text class="message-icon" @click.stop="goToMessageCenter">✉️</text>
        <text v-if="unreadMessages > 0" class="unread-badge">{{ unreadMessages > 99 ? '99+' : unreadMessages }}</text>
      </view>
    </view>
    
    <!-- 核心任务概览 -->
    <view class="task-overview">
      <view class="task-card" @click="goToTaskList('pickup')">
        <text class="card-title">取件任务</text>
        <text class="card-value">{{ taskStats.pickup }}</text>
      </view>
      <view class="task-card" @click="goToTaskList('delivery')">
        <text class="card-title">派件任务</text>
        <text class="card-value">{{ taskStats.delivery }}</text>
      </view>
      <view class="task-card" @click="goToTaskList('overdue')">
        <text class="card-title">超时任务</text>
        <text class="card-value">{{ taskStats.overdue }}</text>
      </view>
    </view>
    
    <!-- 滚动消息栏 -->
    <view class="message-scroll">
      <text class="message-icon">📢</text>
      <swiper class="message-swiper" vertical autoplay interval="3000" circular>
        <swiper-item v-for="(message, index) in messages" :key="index" @click="goToMessageDetail(message.id)">
          <text class="message-text">{{ message.content }}</text>
        </swiper-item>
      </swiper>
    </view>
    
    <!-- 常用功能区 -->
    <view class="quick-actions">
      <view class="action-item" @click="scanCode">
        <text class="action-icon">📷</text>
        <text class="action-label">签收扫描</text>
      </view>
      <view class="action-item" @click="goToAllTasks">
        <text class="action-icon">📋</text>
        <text class="action-label">全部取派</text>
      </view>
      <view class="action-item" @click="goToSignNotice">
        <text class="action-icon">🔔</text>
        <text class="action-label">签收提醒</text>
      </view>
      <view class="action-item" @click="goToFreightQuery">
        <text class="action-icon">💰</text>
        <text class="action-label">运费查询</text>
      </view>
      <view class="action-item" @click="goToExclusivePickup">
        <text class="action-icon">📦</text>
        <text class="action-label">专属取寄</text>
      </view>
    </view>
    
    <!-- 今日数据统计 -->
    <view class="today-stats">
      <view class="stat-item">
        <text class="stat-label">今日已取</text>
        <text class="stat-value">{{ todayStats.picked }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">今日已签</text>
        <text class="stat-value">{{ todayStats.signed }}</text>
      </view>
    </view>
    
    <!-- 待办任务列表 -->
    <view class="pending-tasks">
      <view class="section-header">
        <text class="section-title">待办任务</text>
        <text class="view-all" @click="goToAllTasks">查看更多</text>
      </view>
      <view class="task-list">
        <view class="task-item" v-for="task in pendingTasks" :key="task.id" @click="goToTaskDetail(task.id)">
          <view class="task-info">
            <text class="customer-name">{{ task.customerName }}</text>
            <text class="customer-phone">{{ task.customerPhone }}</text>
            <text class="task-address">{{ task.address }}</text>
            <text class="task-distance">{{ task.distance }}公里</text>
            <text class="task-time">{{ task.time }}</text>
          </view>
          <view class="task-actions">
            <text class="phone-icon" @click.stop="callCustomer(task.customerPhone)">📞</text>
            <text class="message-icon" @click.stop="sendMessage(task.customerPhone)">✉️</text>
            <button v-if="task.type === 'pickup'" class="cancel-btn" @click.stop="cancelTask(task.id)">取消</button>
          </view>
        </view>
      </view>
    </view>
    
  </view>
</template>

<script>
export default {
  data() {
    return {
      unreadMessages: 5,
      taskStats: {
        pickup: 8,
        delivery: 12,
        overdue: 3
      },
      todayStats: {
        picked: 15,
        signed: 22
      },
      messages: [
        { id: 1, content: "您有一个新的取件订单，请及时处理" },
        { id: 2, content: "订单SF1234567890即将超时，请尽快派送" },
        { id: 3, content: "客户张女士催促派送订单SF0987654321" }
      ],
      pendingTasks: [
        {
          id: 'T20230401001',
          type: 'pickup',
          customerName: '李女士',
          customerPhone: '139****9999',
          address: '北京市海淀区某某大厦202室',
          distance: '0.7',
          time: '10:00-12:00'
        },
        {
          id: 'T20230401002',
          type: 'delivery',
          customerName: '王先生',
          customerPhone: '138****8888',
          address: '北京市朝阳区某某小区3号楼501室',
          distance: '1.2',
          time: '14:00-16:00'
        },
        {
          id: 'T20230401003',
          type: 'delivery',
          customerName: '赵先生',
          customerPhone: '137****7777',
          address: '北京市西城区某某街道12号',
          distance: '2.5',
          time: '16:00-18:00'
        }
      ]
    }
  },
  methods: {
    goToSearch() {
      // 跳转到搜索页
      uni.navigateTo({
        url: '/pages/search/index'
      });
    },
    goToMessageCenter() {
      // 跳转到消息中心
      uni.navigateTo({
        url: '/pages/message/index'
      });
    },
    goToTaskList(filter) {
      // 跳转到任务列表页并筛选对应任务
      if (filter === 'pickup') {
        uni.navigateTo({
          url: '/pages/task/list?taskType=pickup'
        });
      } else if (filter === 'delivery') {
        uni.navigateTo({
          url: '/pages/task/list?taskType=delivery'
        });
      } else if (filter === 'overdue') {
        uni.navigateTo({
          url: '/pages/task/list?overdue=true'
        });
      }
    },
    goToMessageDetail(messageId) {
      // 跳转到消息详情
      console.log('跳转到消息详情，ID:', messageId);
    },
    scanCode() {
      // 调起扫码功能
      uni.scanCode({
        success: (res) => {
          uni.showToast({
            title: '扫码成功: ' + res.result,
            icon: 'none'
          });
        },
        fail: () => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          });
        }
      });
    },
    goToAllTasks() {
      // 跳转到全部取派页
      uni.navigateTo({
        url: '/pages/task/all'
      });
    },
    goToSignNotice() {
      // 跳转到签收提醒(消息中心的签收提醒分类)
      uni.navigateTo({
        url: '/pages/message/index?tab=system&subTab=sign'
      });
    },
    goToFreightQuery() {
      // 跳转到运费查询
      uni.navigateTo({
        url: '/pages/freight/index'
      });
    },
    goToExclusivePickup() {
      // 跳转到专属取寄
      uni.navigateTo({
        url: '/pages/exclusive/index'
      });
    },
    goToTaskDetail(taskId) {
      // 跳转到任务详情页
      uni.navigateTo({
        url: '/pages/task/detail?id=' + taskId
      });
    },
    callCustomer(phone) {
      // 拨打客户电话
      uni.makePhoneCall({
        phoneNumber: phone
      });
    },
    sendMessage(phone) {
      // 发送短信
      uni.showToast({
        title: '发送短信功能开发中',
        icon: 'none'
      });
    },
    cancelTask(taskId) {
      // 取消任务
      uni.showModal({
        title: '取消任务',
        content: '确定要取消该任务吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '任务已取消',
              icon: 'success'
            });
          }
        }
      });
    },
    goToPickup() {
      // 跳转到取件页
      uni.switchTab({
        url: '/pages/task/list'
      });
    },
    goToDelivery() {
      // 跳转到派件页
      uni.navigateTo({
        url: '/pages/task/list?taskType=delivery'
      });
    },
    goToProfile() {
      // 跳转到个人中心
      uni.switchTab({
        url: '/pages/profile/index'
      });
    }
  }
}
</script>

<style scoped>
/* 首页容器 */
.home-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-light);
  padding: 0 0 calc(120rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

/* 状态栏占位 */
.status-bar {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: var(--primary-color);
}

/* 顶部搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--primary-color);
  color: var(--text-white);
}

.search-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
}

.search-placeholder {
  flex: 1;
  font-size: 26rpx;
  opacity: 0.8;
}

.search-actions {
  position: relative;
  display: flex;
  align-items: center;
}

.message-icon {
  font-size: 36rpx;
  padding: 10rpx;
}

.unread-badge {
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  background-color: #e74c3c;
  color: white;
  font-size: 20rpx;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 核心任务概览 */
.task-overview {
  display: flex;
  padding: 30rpx;
  background-color: var(--bg-white);
  margin: 20rpx 0;
}

.task-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.task-card:not(:last-child) {
  border-right: 1rpx solid var(--border-color);
}

.card-title {
  font-size: 28rpx;
  color: var(--text-gray);
  margin-bottom: 10rpx;
}

.card-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
}

/* 消息滚动栏 */
.message-scroll {
  display: flex;
  align-items: center;
  padding: 15rpx 30rpx;
  background-color: var(--bg-white);
  margin-bottom: 20rpx;
}

.message-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  color: var(--primary-color);
}

.message-swiper {
  flex: 1;
  height: 40rpx;
}

.message-text {
  font-size: 26rpx;
  color: var(--text-gray);
}

/* 常用功能区 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  background-color: var(--bg-white);
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.action-label {
  font-size: 24rpx;
  color: var(--text-dark);
}

/* 今日数据统计 */
.today-stats {
  display: flex;
  padding: 30rpx;
  background-color: var(--bg-white);
  margin: 20rpx 0;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 28rpx;
  color: var(--text-gray);
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
}

/* 待办任务列表 */
.pending-tasks {
  flex: 1;
  background-color: var(--bg-white);
  margin: 20rpx 0;
  padding: 0 30rpx 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.view-all {
  font-size: 26rpx;
  color: var(--primary-color);
}

.task-list {
  padding: 20rpx 0;
}

.task-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid var(--border-color);
}

.task-item:last-child {
  border-bottom: none;
}

.task-info {
  flex: 1;
}

.customer-name {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.customer-phone {
  font-size: 26rpx;
  color: var(--text-gray);
  margin: 5rpx 0;
}

.task-address {
  font-size: 26rpx;
  color: var(--text-dark);
  margin: 5rpx 0;
}

.task-distance {
  font-size: 24rpx;
  color: var(--primary-color);
  margin: 5rpx 0;
}

.task-time {
  font-size: 24rpx;
  color: var(--text-gray);
}

.task-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
}

.phone-icon, .message-icon {
  font-size: 36rpx;
  margin: 10rpx 0;
}

.cancel-btn {
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  background-color: var(--bg-gray);
  border-radius: 6rpx;
  color: var(--text-dark);
}

/* 底部导航栏 */
.bottom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: var(--bg-white);
  border-top: 1rpx solid var(--border-color);
  display: flex;
  padding: 10rpx 0 env(safe-area-inset-bottom);
  z-index: 999;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: var(--text-gray);
}

.tab-item.active {
  color: var(--primary-color);
}

.tab-icon {
  font-size: 36rpx;
  margin-bottom: 6rpx;
}

.scan-center {
  flex: 1.5;
  background-color: var(--primary-color);
  border-radius: 50rpx;
  margin: 0 20rpx;
  color: var(--text-white);
}
</style>