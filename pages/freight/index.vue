<template>
  <view class="freight-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <text class="header-title">运费查询</text>
      <view class="header-right"></view>
    </view>
    
    <!-- 查询表单 -->
    <view class="query-form">
      <view class="form-group">
        <text class="form-label">寄件地</text>
        <view class="location-selector" @click="selectPickupLocation">
          <text class="location-text">{{ pickupLocation || '请选择寄件地' }}</text>
          <text class="arrow">▼</text>
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">收件地</text>
        <view class="location-selector" @click="selectDeliveryLocation">
          <text class="location-text">{{ deliveryLocation || '请选择收件地' }}</text>
          <text class="arrow">▼</text>
        </view>
      </view>
      
      <view class="form-row">
        <view class="form-group half">
          <text class="form-label">重量(kg)</text>
          <input 
            class="form-input" 
            type="digit" 
            v-model="packageWeight" 
            placeholder="请输入物品重量">
        </view>
        <view class="form-group half">
          <text class="form-label">体积(cm³)</text>
          <input 
            class="form-input" 
            type="digit" 
            v-model="packageVolume" 
            placeholder="请输入物品体积">
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">物品类型</text>
        <view class="type-selector">
          <view 
            class="type-item" 
            v-for="type in packageTypes" 
            :key="type.value"
            :class="{ active: selectedPackageType === type.value }"
            @click="selectPackageType(type.value)"
          >
            {{ type.label }}
          </view>
        </view>
      </view>
      
      <button class="query-btn" @click="calculateFreight">查询运费</button>
    </view>
    
    <!-- 查询结果 -->
    <view class="result-section" v-if="freightResult">
      <view class="section-title">费用明细</view>
      <view class="fee-item">
        <text class="fee-label">首重费用</text>
        <text class="fee-value">¥{{ freightResult.baseFee }}</text>
      </view>
      <view class="fee-item">
        <text class="fee-label">续重费用</text>
        <text class="fee-value">¥{{ freightResult.additionalFee }}</text>
      </view>
      <view class="fee-item" v-if="freightResult.volumeFee > 0">
        <text class="fee-label">体积费用</text>
        <text class="fee-value">¥{{ freightResult.volumeFee }}</text>
      </view>
      <view class="fee-item" v-if="freightResult.specialItemFee > 0">
        <text class="fee-label">特殊物品费</text>
        <text class="fee-value">¥{{ freightResult.specialItemFee }}</text>
      </view>
      <view class="fee-divider"></view>
      <view class="fee-item total">
        <text class="fee-label">总计</text>
        <text class="fee-value total-value">¥{{ freightResult.totalFee }}</text>
      </view>
    </view>
    
    <!-- 运费说明 -->
    <view class="freight-info">
      <view class="info-title">运费说明</view>
      <view class="info-content">
        <text class="info-text">1. 首重1kg内费用为¥8，续重每1kg费用为¥2</text>
        <text class="info-text">2. 体积重量 = 长×宽×高/6000，计费重量取实际重量与体积重量较大者</text>
        <text class="info-text">3. 特殊物品(如易碎品、液体等)需额外收取¥5处理费</text>
        <text class="info-text">4. 以上费用仅供参考，实际费用以快递员收取为准</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      pickupLocation: '北京市',
      deliveryLocation: '上海市',
      packageWeight: '',
      packageVolume: '',
      selectedPackageType: 'document',
      packageTypes: [
        { label: '文件', value: 'document' },
        { label: '电子产品', value: 'electronics' },
        { label: '衣物', value: 'clothing' },
        { label: '食品', value: 'food' },
        { label: '易碎品', value: 'fragile' },
        { label: '液体', value: 'liquid' }
      ],
      freightResult: null
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    selectPickupLocation() {
      // 选择寄件地
      uni.showToast({
        title: '选择寄件地功能开发中',
        icon: 'none'
      });
    },
    selectDeliveryLocation() {
      // 选择收件地
      uni.showToast({
        title: '选择收件地功能开发中',
        icon: 'none'
      });
    },
    selectPackageType(type) {
      this.selectedPackageType = type;
    },
    calculateFreight() {
      // 验证输入
      if (!this.pickupLocation || !this.deliveryLocation) {
        uni.showToast({
          title: '请选择寄件地和收件地',
          icon: 'none'
        });
        return;
      }
      
      if (!this.packageWeight && !this.packageVolume) {
        uni.showToast({
          title: '请输入重量或体积',
          icon: 'none'
        });
        return;
      }
      
      // 模拟计算运费
      const weight = parseFloat(this.packageWeight) || 0;
      const volume = parseFloat(this.packageVolume) || 0;
      
      // 基础费用
      const baseFee = 8; // 首重费用
      
      // 续重费用
      const additionalWeight = Math.max(0, weight - 1);
      const additionalFee = additionalWeight * 2;
      
      // 体积费用
      const volumeWeight = volume / 6000;
      const volumeFee = volumeWeight > weight ? (volumeWeight - 1) * 2 : 0;
      
      // 特殊物品费用
      const specialItemFee = ['fragile', 'liquid'].includes(this.selectedPackageType) ? 5 : 0;
      
      // 总费用
      const totalFee = (baseFee + additionalFee + volumeFee + specialItemFee).toFixed(2);
      
      this.freightResult = {
        baseFee: baseFee.toFixed(2),
        additionalFee: additionalFee.toFixed(2),
        volumeFee: volumeFee.toFixed(2),
        specialItemFee: specialItemFee.toFixed(2),
        totalFee
      };
      
      uni.showToast({
        title: '计算完成',
        icon: 'success'
      });
    }
  }
}
</script>

<style scoped>
/* 容器 */
.freight-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, #1e6eeb, #0d5cb6);
  color: var(--text-white);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  padding: 10rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.header-left:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.2);
}

.back-icon {
  font-size: 36rpx;
  margin-right: 8rpx;
  color: white;
}

.back-text {
  font-size: 28rpx;
  color: white;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.header-right {
  width: 80rpx;
}

/* 查询表单 */
.query-form {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.form-group {
  margin-bottom: 30rpx;
}

.form-row {
  display: flex;
  gap: 20rpx;
}

.half {
  flex: 1;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: var(--text-dark);
  margin-bottom: 15rpx;
}

.location-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
}

.location-text {
  font-size: 26rpx;
  color: var(--text-dark);
}

.arrow {
  font-size: 20rpx;
  color: var(--text-gray);
}

.form-input {
  width: 100%;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  box-sizing: border-box;
  background-color: #fff;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #1e6eeb;
  box-shadow: 0 0 20rpx rgba(30, 110, 235, 0.2);
  outline: none;
}

.type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.type-item {
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  background-color: var(--bg-light);
  border-radius: 10rpx;
  color: var(--text-dark);
}

.type-item.active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.query-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #1e6eeb, #0d5cb6);
  color: var(--text-white);
  font-size: 30rpx;
  border-radius: 10rpx;
  border: none;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(30, 110, 235, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
}

.query-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(30, 110, 235, 0.4);
}

/* 查询结果 */
.result-section {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.fee-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.fee-label {
  font-size: 26rpx;
  color: var(--text-dark);
}

.fee-value {
  font-size: 26rpx;
  color: var(--text-dark);
  font-weight: 500;
}

.fee-divider {
  height: 1rpx;
  background-color: var(--border-color);
  margin: 20rpx 0;
}

.total {
  font-weight: bold;
}

.total-value {
  color: #e74c3c;
  font-size: 32rpx;
}

/* 运费说明 */
.freight-info {
  background-color: var(--bg-white);
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-light);
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-text {
  font-size: 24rpx;
  color: var(--text-gray);
  line-height: 1.5;
}
</style>