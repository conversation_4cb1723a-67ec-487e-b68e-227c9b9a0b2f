<template>
  <view class="theme-switcher">
    <button 
      v-for="(theme, index) in themes" 
      :key="index"
      :class="['theme-btn', { active: currentTheme === theme }]"
      @click="switchTheme(theme)">
      {{ theme }}
    </button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      themes: ['light', 'dark', 'system'],
      currentTheme: 'system'
    };
  },
  methods: {
    switchTheme(theme) {
      this.currentTheme = theme;
      uni.setStorageSync('theme', theme);
      this.applyTheme();
    },
    applyTheme() {
      const theme = this.currentTheme;
      if (theme === 'system') {
        // 使用系统主题
        document.body.classList.remove('light-theme', 'dark-theme');
      } else {
        document.body.classList.add(`${theme}-theme`);
      }
    }
  },
  mounted() {
    const savedTheme = uni.getStorageSync('theme') || 'system';
    this.switchTheme(savedTheme);
  }
};
</script>

<style>
.theme-switcher {
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
}

.theme-btn {
  padding: 10rpx 20rpx;
  margin: 0 10rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}

.theme-btn.active {
  background-color: #4caf50;
  color: #fff;
}
</style>