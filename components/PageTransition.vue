<template>
  <view class="transition-container" :class="transitionClass">
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'PageTransition',
  data() {
    return {
      transitionClass: ''
    };
  },
  methods: {
    slideInRight() {
      this.transitionClass = 'slide-in-right';
      setTimeout(() => {
        this.transitionClass = '';
      }, 300);
    },
    
    slideOutLeft() {
      this.transitionClass = 'slide-out-left';
      setTimeout(() => {
        this.transitionClass = '';
      }, 300);
    }
  }
};
</script>

<style scoped>
.transition-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out forwards;
}

.slide-out-left {
  animation: slideOutLeft 0.3s ease-out forwards;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}
</style>