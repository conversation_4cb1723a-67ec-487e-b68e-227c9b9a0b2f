<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		globalData: {
			pageTransition: 'slide'
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import "@/uni.scss";
	
	/* 全局主题样式 */
	:root {
	  /* 主题颜色 */
	  --primary-color: #3498db;      /* 主色调 - 现代蓝色 */
	  --secondary-color: #2c3e50;    /* 辅助色调 - 深蓝灰色 */
	  --accent-color: #1abc9c;       /* 强调色 - 青绿色 */
	  --primary-hover: #2980b9;      /* 主色调悬停状态 */

	  /* 背景颜色 */
	  --bg-white: #ffffff;           /* 白色背景 */
	  --bg-light: #f8f9fa;           /* 浅色半透明背景 */
	  --bg-gray: #bdc3c7;            /* 灰色背景 - 禁用状态按钮 */
	  --bg-dark: #ecf0f1;            /* 深灰色背景 */

	  /* 文本颜色 */
	  --text-white: #ffffff;         /* 白色文本 */
	  --text-gray: #7f8c8d;          /* 灰色文本 */
	  --text-dark: #2c3e50;          /* 深色文本 */
	  --text-error: #e74c3c;         /* 错误文本颜色 */

	  /* 边框颜色 */
	  --border-color: #dcdde1;       /* 输入框边框颜色 */
	  --border-focus: #3498db;       /* 聚焦边框颜色 */

	  /* 阴影颜色 */
	  --shadow-color: rgba(0, 0, 0, 0.1);     /* 普通阴影 */
	  --shadow-light: rgba(0, 0, 0, 0.1);     /* 浅色阴影 */
	  --shadow-dark: rgba(0, 0, 0, 0.2);      /* 深色阴影 */
	  --shadow-primary: rgba(52, 152, 219, 0.3); /* 主题色阴影 */
	  --shadow-accent: rgba(26, 188, 156, 0.3);  /* 强调色阴影 */
	}
	
	/* 深色主题 */
	[data-theme="dark"] {
	  --primary-color: #1a1a2e;
	  --secondary-color: #16213e;
	  --accent-color: #1abc9c;
	  --text-color: #ecf0f1;
	  --text-white: #ecf0f1;
	  --text-gray: #95a5a6;
	  --bg-color: #1a1a2e;
	  --bg-light: #16213e;
	  --bg-dark: #0f3460;
	  --border-color: #2c3e50;
	  --border-focus: #3498db;
	  --shadow-color: rgba(0, 0, 0, 0.3);
	  --shadow-dark: rgba(0, 0, 0, 0.5);
	}

	/* 绿色主题 */
	[data-theme="green"] {
	  --primary-color: #2ecc71;
	  --secondary-color: #27ae60;
	  --accent-color: #f1c40f;
	  --text-color: #2c3e50;
	  --text-white: #ffffff;
	  --text-gray: #7f8c8d;
	  --bg-color: #ffffff;
	  --bg-light: #f8fff9;
	  --bg-dark: #e8f8f5;
	  --border-color: #d1f2eb;
	  --border-focus: #2ecc71;
	  --shadow-color: rgba(46, 204, 113, 0.1);
	  --shadow-dark: rgba(46, 204, 113, 0.2);
	}

	/* 紫色主题 */
	[data-theme="purple"] {
	  --primary-color: #9b59b6;
	  --secondary-color: #8e44ad;
	  --accent-color: #e74c3c;
	  --text-color: #2c3e50;
	  --text-white: #ffffff;
	  --text-gray: #7f8c8d;
	  --bg-color: #ffffff;
	  --bg-light: #fdf7ff;
	  --bg-dark: #f5eef8;
	  --border-color: #e8daef;
	  --border-focus: #9b59b6;
	  --shadow-color: rgba(155, 89, 182, 0.1);
	  --shadow-dark: rgba(155, 89, 182, 0.2);
	}
	
	/* 页面切换动画 */
	.slide-in-right-enter-active,
	.slide-in-right-leave-active {
	  transition: all 0.3s ease;
	  position: absolute;
	  width: 100%;
	  height: 100%;
	}
	
	.slide-in-right-enter {
	  transform: translateX(100%);
	  opacity: 0;
	}
	
	.slide-in-right-leave-to {
	  transform: translateX(-100%);
	  opacity: 0;
	}
</style>