/*
 * 登录页面样式文件
 * 定义登录页面使用的所有CSS样式
 */

/* 全局样式 - 防止页面滚动 */
html[data-v-e4e4508d], body[data-v-e4e4508d] {
  height: 100%;
  overflow: hidden;
  background-color: var(--bg-light);
}
[data-v-e4e4508d]:root {
  /* 主题颜色 */
  --primary-color: #3498db;      /* 主色调 - 现代蓝色 */
  --secondary-color: #2c3e50;    /* 辅助色调 - 深蓝灰色 */
  --accent-color: #1abc9c;       /* 强调色 - 青绿色 */
  --primary-hover: #2980b9;      /* 主色调悬停状态 */

  /* 背景颜色 */
  --bg-white: #ffffff;           /* 白色背景 */
  --bg-light: #f8f9fa;           /* 浅色半透明背景 */
  --bg-gray: #bdc3c7;            /* 灰色背景 - 禁用状态按钮 */
  --bg-dark: #ecf0f1;            /* 深灰色背景 */

  /* 文本颜色 */
  --text-white: #ffffff;         /* 白色文本 */
  --text-gray: #7f8c8d;          /* 灰色文本 */
  --text-dark: #2c3e50;          /* 深色文本 */
  --text-error: #e74c3c;         /* 错误文本颜色 */

  /* 边框颜色 */
  --border-color: #dcdde1;       /* 输入框边框颜色 */
  --border-focus: #3498db;       /* 聚焦边框颜色 */

  /* 阴影颜色 */
  --shadow-color: rgba(0, 0, 0, 0.1);     /* 普通阴影 */
  --shadow-light: rgba(0, 0, 0, 0.1);     /* 浅色阴影 */
  --shadow-dark: rgba(0, 0, 0, 0.2);      /* 深色阴影 */
  --shadow-primary: rgba(52, 152, 219, 0.3); /* 主题色阴影 */
  --shadow-accent: rgba(26, 188, 156, 0.3);  /* 强调色阴影 */
}

/* 登录容器 - 优化响应式布局 */
.login-container[data-v-e4e4508d] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 1.25rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  /* 使用安全区域适配，避免与状态栏重叠 */
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  padding-bottom: max(env(safe-area-inset-bottom), 1.25rem);
  transition: all 0.3s ease;
}

/* 登录模块通用样式 */
.login-container[data-v-e4e4508d] {
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  min-height: 100vh;
  background-color: rgba(255, 255, 255, 0.9);
}
.logo[data-v-e4e4508d] {
  margin-top: 1.875rem;
  text-align: center;
}
.form-group[data-v-e4e4508d] {
  padding: 0 1.25rem;
  margin-bottom: 0.9375rem;
}
.input-field[data-v-e4e4508d] {
  height: 2.5rem;
  line-height: 2.5rem;
  border: 1px solid #ddd;
  border-radius: 0.3125rem;
  padding: 0 0.625rem;
}

/* 状态栏样式 */
.status-bar[data-v-e4e4508d] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: transparent;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

/* 登录容器装饰元素 */
.login-container[data-v-e4e4508d]::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  transform: rotate(30deg);
  z-index: 0;
}

/* 登录头部 */
.login-header[data-v-e4e4508d] {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0.625rem;
  position: relative;
  z-index: 1;
  transform: translateY(-1.25rem);
}

/* Logo图标 */
.logo[data-v-e4e4508d] {
  width: 5rem;
  height: 5rem;
  border-radius: 0.9375rem;
  box-shadow: 0 0.3125rem 0.9375rem var(--shadow-dark);
  margin-bottom: 0.9375rem;
  margin-top: 0.3125rem;
  background-color: var(--bg-white);
  padding: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 2.5rem;
  font-weight: bold;
}

/* 应用标题 */
.app-title[data-v-e4e4508d] {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-white);
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.2);
  letter-spacing: 0.0625rem;
}

/* 登录标签页容器 */
.login-tabs[data-v-e4e4508d] {
  display: flex;
  justify-content: center;
  margin-bottom: 0.625rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 1.5625rem;
  padding: 0.15625rem;
  max-width: 15.625rem;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* 标签项 */
.tab-item[data-v-e4e4508d] {
  flex: 1;
  text-align: center;
  padding: 0.625rem 0;
  font-size: 1rem;
  color: var(--text-white);
  border-radius: 1.5625rem;
  transition: all 0.3s ease;
  cursor: pointer;
  font-weight: 500;
}

/* 激活的标签项 */
.tab-item.active[data-v-e4e4508d] {
  background-color: var(--bg-white);
  color: var(--primary-color);
  font-weight: 700;
  box-shadow: 0 0.125rem 0.3125rem rgba(0, 0, 0, 0.1);
}

/* 登录表单 - 优化响应式布局 */
.login-form[data-v-e4e4508d] {
  width: 100%;
  max-width: 18.75rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 0.625rem;
  padding: 1.25rem;
  box-shadow: 0 0.46875rem 1.09375rem rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(0.3125rem);
          backdrop-filter: blur(0.3125rem);
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  margin-top: 0.3125rem;
  margin-bottom: 1.25rem;
  transition: all 0.3s ease;
}

/* 输入框组 */
.input-group[data-v-e4e4508d] {
  position: relative;
  margin-bottom: 0.9375rem;
}

/* 登录输入框 */
.login-input[data-v-e4e4508d] {
  width: 100%;
  height: 2.5rem;
  padding: 0 0.9375rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.46875rem;
  font-size: 0.875rem;
  box-sizing: border-box;
  background-color: var(--bg-white);
  color: var(--text-dark);
  transition: all 0.3s ease;
}

/* 输入框聚焦状态 */
.login-input[data-v-e4e4508d]:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0.09375rem var(--shadow-primary);
  outline: none;
}

/* 密码组容器 */
.password-group[data-v-e4e4508d] {
  display: flex;
  align-items: center;
}

/* 密码切换按钮 */
.password-toggle[data-v-e4e4508d] {
  position: absolute;
  right: 0.78125rem;
  color: var(--primary-color);
  font-size: 0.8125rem;
  z-index: 2;
  background-color: var(--bg-white);
  padding: 0 0.3125rem;
  cursor: pointer;
}

/* 眼睛图标容器 */
.eye-icon[data-v-e4e4508d] {
  position: absolute;
  right: 0.78125rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  background-color: #FFFFFF;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
}

/* 眼睛图标样式 */
.eye-icon[data-v-e4e4508d]::before {
  content: "";
  display: inline-block;
  width: 0.75rem;
  height: 0.75rem;
  border: 0.0625rem solid var(--primary-color);
  border-radius: 50%;
  position: relative;
}

/* 眼睛瞳孔 */
.eye-icon[data-v-e4e4508d]::after {
  content: "";
  display: inline-block;
  width: 0.25rem;
  height: 0.25rem;
  background-color: var(--primary-color);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 闭眼图标样式（斜线） */
.eye-icon.closed[data-v-e4e4508d]::before {
  content: "";
  display: inline-block;
  width: 0.75rem;
  height: 0.75rem;
  border: 0.0625rem solid var(--primary-color);
  border-radius: 50%;
  position: relative;
}
.eye-icon.closed[data-v-e4e4508d]::after {
  content: "";
  display: block;
  width: 0.0625rem;
  height: 0.9375rem;
  background-color: var(--primary-color);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  border-radius: 0;
}

/* 验证码组容器 */
.verification-group[data-v-e4e4508d] {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.625rem;
}

/* 验证码输入框 */
.verification-input[data-v-e4e4508d] {
  flex: 1;
}

/* 验证码按钮 */
.verification-button[data-v-e4e4508d] {
  width: 6.25rem;
  height: 2.5rem;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  color: var(--text-white);
  border: none;
  border-radius: 0.46875rem;
  font-size: 0.8125rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  padding: 0;
  box-sizing: border-box;
  font-weight: 500;
  box-shadow: 0 0.125rem 0.3125rem var(--shadow-primary);
  transition: all 0.3s ease;
}

/* 验证码按钮悬停状态 */
.verification-button[data-v-e4e4508d]:hover:not(:disabled) {
  transform: translateY(-0.0625rem);
  box-shadow: 0 0.1875rem 0.46875rem var(--shadow-primary);
}

/* 验证码按钮禁用状态 */
.verification-button[data-v-e4e4508d]:disabled {
  background: linear-gradient(to right, var(--bg-gray), #95a5a6);
  cursor: not-allowed;
  box-shadow: none;
}

/* 登录按钮 */
.login-button[data-v-e4e4508d] {
  width: 100%;
  height: 2.5rem;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  color: var(--text-white);
  border: none;
  border-radius: 0.46875rem;
  font-size: 1rem;
  font-weight: 700;
  margin-top: 0.625rem;
  box-shadow: 0 0.125rem 0.46875rem var(--shadow-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.0625rem;
  box-sizing: border-box;
}

/* 登录按钮悬停状态 */
.login-button[data-v-e4e4508d]:hover:not(:disabled) {
  transform: translateY(-0.09375rem);
  box-shadow: 0 0.21875rem 0.625rem var(--shadow-dark);
}

/* 登录按钮禁用状态 */
.login-button[data-v-e4e4508d]:disabled {
  background: linear-gradient(to right, var(--bg-gray), #95a5a6);
  cursor: not-allowed;
  box-shadow: none;
}

/* 错误信息 */
.error-message[data-v-e4e4508d] {
  color: var(--text-error);
  font-size: 0.75rem;
  margin-top: 0.625rem;
  text-align: center;
  font-weight: 500;
}

/* ===== 响应式媒体查询 ===== */

/* 平板设备（中等屏幕 - 介于手机和大屏幕之间） */
@media screen and (min-width: 600px) and (max-width: 767px) {
  /* 调整Logo大小 */
.logo[data-v-e4e4508d] {
    width: 5.3125rem;
    height: 5.3125rem;
}
  
  /* 调整应用标题大小 */
.app-title[data-v-e4e4508d] {
    font-size: 1.625rem;
}
  
  /* 调整登录表单 */
.login-form[data-v-e4e4508d] {
    padding: 1.40625rem;
    max-width: 20.3125rem;
}
  
  /* 调整输入框高度 */
.login-input[data-v-e4e4508d] {
    height: 2.65625rem;
    font-size: 0.9375rem;
}
  
  /* 调整登录按钮高度 */
.login-button[data-v-e4e4508d] {
    height: 2.65625rem;
    font-size: 1.0625rem;
}
  
  /* 调整验证码按钮大小 */
.verification-button[data-v-e4e4508d] {
    width: 6.5625rem;
    height: 2.65625rem;
    font-size: 0.8125rem;
}
  
  /* 调整登录标签字体大小 */
.tab-item[data-v-e4e4508d] {
    font-size: 1.0625rem;
    padding: 0.6875rem 0;
}
}

/* 小屏幕设备（手机横屏或小型平板） */
@media screen and (max-width: 375px) {
  /* 调整Logo大小 */
.logo[data-v-e4e4508d] {
    width: 4.375rem;
    height: 4.375rem;
}
  
  /* 调整应用标题大小 */
.app-title[data-v-e4e4508d] {
    font-size: 1.3125rem;
}
  
  /* 调整登录表单内边距 */
.login-form[data-v-e4e4508d] {
    padding: 0.9375rem;
    transform: scale(0.95);
}
  
  /* 调整输入框高度 */
.login-input[data-v-e4e4508d] {
    height: 2.1875rem;
    font-size: 0.8125rem;
}
  
  /* 调整登录按钮高度 */
.login-button[data-v-e4e4508d] {
    height: 2.1875rem;
    font-size: 0.875rem;
}
  
  /* 调整验证码按钮大小 */
.verification-button[data-v-e4e4508d] {
    width: 5.625rem;
    height: 2.1875rem;
    font-size: 0.75rem;
}
  
  /* 调整登录标签字体大小 */
.tab-item[data-v-e4e4508d] {
    font-size: 0.875rem;
    padding: 0.46875rem 0;
}
  
  /* 表单元素间距 */
.input-group[data-v-e4e4508d] {
    margin-bottom: 0.78125rem;
}
  
  /* 调整登录容器布局 */
.login-container[data-v-e4e4508d] {
    justify-content: center;
}
}

/* 大屏幕设备（平板及以上） */
@media screen and (min-width: 768px) {
  /* 调整整体布局 */
.login-container[data-v-e4e4508d] {
    justify-content: center;
}
  
  /* 添加卡片式效果 */
.login-form[data-v-e4e4508d] {
    transform: translateY(0);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.login-form[data-v-e4e4508d]:hover {
    transform: translateY(-0.3125rem);
    box-shadow: 0 0.625rem 1.40625rem rgba(0, 0, 0, 0.25);
}
  /* 调整Logo大小 */
.logo[data-v-e4e4508d] {
    width: 5.625rem;
    height: 5.625rem;
}
  
  /* 调整应用标题大小 */
.app-title[data-v-e4e4508d] {
    font-size: 1.75rem;
}
  
  /* 调整登录表单内边距 */
.login-form[data-v-e4e4508d] {
    padding: 1.5625rem;
    max-width: 21.875rem;
}
  
  /* 调整输入框高度 */
.login-input[data-v-e4e4508d] {
    height: 2.8125rem;
    font-size: 1rem;
}
  
  /* 调整登录按钮高度 */
.login-button[data-v-e4e4508d] {
    height: 2.8125rem;
    font-size: 1.125rem;
}
  
  /* 调整验证码按钮大小 */
.verification-button[data-v-e4e4508d] {
    width: 6.875rem;
    height: 2.8125rem;
    font-size: 0.875rem;
}
  
  /* 调整登录标签字体大小 */
.tab-item[data-v-e4e4508d] {
    font-size: 1.125rem;
    padding: 0.78125rem 0;
}
}

/* 超小屏幕设备 */
@media screen and (max-width: 320px) {
  /* 整体缩放适配 */
.login-container[data-v-e4e4508d] {
    padding: 0 0.46875rem;
}
  
  /* 表单进一步缩小 */
.login-form[data-v-e4e4508d] {
    padding: 0.78125rem;
    transform: scale(0.92);
}
  
  /* 紧凑布局 */
.logo[data-v-e4e4508d] {
    margin-top: 0.15625rem;
    margin-bottom: 0.625rem;
}
  
  /* 减少元素间距 */
.login-header[data-v-e4e4508d] {
    margin-bottom: 0.46875rem;
}
.login-tabs[data-v-e4e4508d] {
    margin-bottom: 0.46875rem;
}
  
  /* 增大触摸目标 */
.login-button[data-v-e4e4508d],
  .verification-button[data-v-e4e4508d],
  .eye-icon[data-v-e4e4508d] {
    min-height: 1.875rem;
}
  /* 调整登录容器内边距 */
.login-container[data-v-e4e4508d] {
    padding: 0 0.625rem;
}
}