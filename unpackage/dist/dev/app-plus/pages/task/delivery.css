
.theme-switcher[data-v-d495b30c] {
  display: flex;
  justify-content: center;
  margin-top: 0.625rem;
}
.theme-btn[data-v-d495b30c] {
  padding: 0.3125rem 0.625rem;
  margin: 0 0.3125rem;
  border-radius: 0.25rem;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}
.theme-btn.active[data-v-d495b30c] {
  background-color: #4caf50;
  color: #fff;
}


.delivery-container[data-v-f4f36846] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  padding-bottom: 1.25rem;
  box-sizing: border-box;
}
.status-bar[data-v-f4f36846] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: #3498db;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}
.header[data-v-f4f36846] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: #3498db;
  color: white;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}
.header-left[data-v-f4f36846] {
  display: flex;
  align-items: center;
}
.back-icon[data-v-f4f36846] {
  font-size: 1.125rem;
  margin-right: 0.3125rem;
}
.back-text[data-v-f4f36846] {
  font-size: 0.9375rem;
}
.header-title[data-v-f4f36846] {
  font-size: 1.125rem;
  font-weight: bold;
}
.header-right[data-v-f4f36846] {
  width: 1.5625rem;
}
.task-info[data-v-f4f36846] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.task-header[data-v-f4f36846] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.9375rem;
}
.task-id[data-v-f4f36846] {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
}
.task-status[data-v-f4f36846] {
  font-size: 0.75rem;
  padding: 0.25rem 0.625rem;
  border-radius: 0.625rem;
}
.status-delivery[data-v-f4f36846] {
  background-color: #d4edda;
  color: #155724;
}
.task-details[data-v-f4f36846] {
  display: flex;
  flex-direction: column;
  gap: 0.46875rem;
}
.detail-item[data-v-f4f36846] {
  display: flex;
}
.label[data-v-f4f36846] {
  width: 5rem;
  font-size: 0.875rem;
  color: #666;
}
.value[data-v-f4f36846] {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
}
.phone-number[data-v-f4f36846] {
  color: #3498db;
}
.delivery-section[data-v-f4f36846] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.section-title[data-v-f4f36846] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.9375rem;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid #eee;
}
.form-group[data-v-f4f36846] {
  margin-bottom: 0.9375rem;
}
.form-group[data-v-f4f36846]:last-child {
  margin-bottom: 0;
}
.form-label[data-v-f4f36846] {
  display: block;
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.46875rem;
  font-weight: 500;
}
.form-input[data-v-f4f36846] {
  width: 100%;
  height: 2.5rem;
  padding: 0 0.625rem;
  border: 0.03125rem solid #ddd;
  border-radius: 0.3125rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.form-textarea[data-v-f4f36846] {
  width: 100%;
  height: 4.6875rem;
  padding: 0.625rem;
  border: 0.03125rem solid #ddd;
  border-radius: 0.3125rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.delivery-methods[data-v-f4f36846] {
  display: flex;
  gap: 0.625rem;
}
.method-item[data-v-f4f36846] {
  flex: 1;
  text-align: center;
  padding: 0.625rem;
  border: 0.03125rem solid #ddd;
  border-radius: 0.3125rem;
  font-size: 0.875rem;
  color: #666;
}
.method-item.active[data-v-f4f36846] {
  background-color: #3498db;
  color: white;
  border-color: #3498db;
}
.photo-upload[data-v-f4f36846] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.625rem;
}
.photo-item[data-v-f4f36846] {
  position: relative;
  width: 4.6875rem;
  height: 4.6875rem;
}
.photo[data-v-f4f36846] {
  width: 100%;
  height: 100%;
  border-radius: 0.3125rem;
}
.remove-photo[data-v-f4f36846] {
  position: absolute;
  top: -0.46875rem;
  right: -0.46875rem;
  width: 1.25rem;
  height: 1.25rem;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9375rem;
}
.photo-upload-btn[data-v-f4f36846] {
  width: 4.6875rem;
  height: 4.6875rem;
  border: 0.03125rem dashed #ddd;
  border-radius: 0.3125rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-icon[data-v-f4f36846] {
  font-size: 1.875rem;
  color: #999;
}
.upload-text[data-v-f4f36846] {
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.3125rem;
}
.action-buttons[data-v-f4f36846] {
  display: flex;
  padding: 0.625rem;
  gap: 0.625rem;
}
.action-btn[data-v-f4f36846] {
  flex: 1;
  padding: 0.625rem;
  border-radius: 0.3125rem;
  font-size: 0.9375rem;
  border: none;
  font-weight: bold;
}
.primary[data-v-f4f36846] {
  background-color: #3498db;
  color: white;
}
.secondary[data-v-f4f36846] {
  background-color: white;
  color: #333;
  border: 0.03125rem solid #ddd;
}
