
.task-container[data-v-b071c5a1] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  padding-bottom: 3.75rem;
  box-sizing: border-box;
}
.status-bar[data-v-b071c5a1] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: #3498db;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}
.header[data-v-b071c5a1] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: #3498db;
  color: white;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}
.header-title[data-v-b071c5a1] {
  font-size: 1.125rem;
  font-weight: bold;
}
.refresh-btn[data-v-b071c5a1] {
  font-size: 0.875rem;
  padding: 0.3125rem 0.625rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.3125rem;
}
.filter-container[data-v-b071c5a1] {
  display: flex;
  background-color: white;
  padding: 0.625rem;
  box-shadow: 0 0.0625rem 0.15625rem rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 9;
}
.filter-item[data-v-b071c5a1] {
  flex: 1;
  text-align: center;
  padding: 0.46875rem 0;
  font-size: 0.875rem;
  color: #666;
  border-radius: 0.3125rem;
  margin: 0 0.3125rem;
}
.filter-item.active[data-v-b071c5a1] {
  background-color: #3498db;
  color: white;
  font-weight: bold;
}
.task-list[data-v-b071c5a1] {
  flex: 1;
  padding: 0.625rem;
  overflow-y: auto;
}
.task-item[data-v-b071c5a1] {
  background-color: white;
  border-radius: 0.46875rem;
  padding: 0.78125rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.task-header[data-v-b071c5a1] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid #eee;
  margin-bottom: 0.625rem;
}
.task-id[data-v-b071c5a1] {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
}
.task-status[data-v-b071c5a1] {
  font-size: 0.75rem;
  padding: 0.15625rem 0.46875rem;
  border-radius: 0.3125rem;
}
.status-pending[data-v-b071c5a1] {
  background-color: #fff3cd;
  color: #856404;
}
.status-pickup[data-v-b071c5a1] {
  background-color: #cce5ff;
  color: #004085;
}
.status-delivery[data-v-b071c5a1] {
  background-color: #d4edda;
  color: #155724;
}
.task-content[data-v-b071c5a1] {
  display: flex;
  flex-direction: column;
  gap: 0.46875rem;
}
.label[data-v-b071c5a1] {
  font-size: 0.75rem;
  color: #999;
  margin-right: 0.3125rem;
}
.address[data-v-b071c5a1], .time[data-v-b071c5a1], .customer[data-v-b071c5a1] {
  font-size: 0.8125rem;
  color: #333;
}
.task-footer[data-v-b071c5a1] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.625rem;
  padding-top: 0.625rem;
  border-top: 0.03125rem solid #eee;
}
.distance[data-v-b071c5a1] {
  font-size: 0.75rem;
  color: #999;
}
.action-btn[data-v-b071c5a1] {
  font-size: 0.8125rem;
  padding: 0.3125rem 0.78125rem;
  border-radius: 0.3125rem;
  color: white;
  border: none;
}
.action-start[data-v-b071c5a1] {
  background-color: #3498db;
}
.action-pickup[data-v-b071c5a1] {
  background-color: #28a745;
}
.action-delivery[data-v-b071c5a1] {
  background-color: #ffc107;
  color: #333;
}
.empty-state[data-v-b071c5a1] {
  text-align: center;
  padding: 3.125rem 0;
}
.empty-text[data-v-b071c5a1] {
  font-size: 0.9375rem;
  color: #999;
}
.tab-bar[data-v-b071c5a1] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3.75rem;
  background-color: white;
  display: flex;
  border-top: 0.03125rem solid #eee;
  padding-bottom: env(safe-area-inset-bottom);
}
.tab-item[data-v-b071c5a1] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}
.tab-item.active[data-v-b071c5a1] {
  color: #3498db;
}
.tab-icon[data-v-b071c5a1] {
  font-size: 1.25rem;
  margin-bottom: 0.15625rem;
}
