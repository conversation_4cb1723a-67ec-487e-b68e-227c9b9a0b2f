
.theme-switcher[data-v-d495b30c] {
  display: flex;
  justify-content: center;
  margin-top: 0.625rem;
}
.theme-btn[data-v-d495b30c] {
  padding: 0.3125rem 0.625rem;
  margin: 0 0.3125rem;
  border-radius: 0.25rem;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}
.theme-btn.active[data-v-d495b30c] {
  background-color: #4caf50;
  color: #fff;
}


.task-detail-container[data-v-43b93a3d] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  padding-bottom: 1.25rem;
  box-sizing: border-box;
}
.status-bar[data-v-43b93a3d] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: #3498db;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}
.header[data-v-43b93a3d] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: #3498db;
  color: white;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}
.header-left[data-v-43b93a3d] {
  display: flex;
  align-items: center;
}
.back-icon[data-v-43b93a3d] {
  font-size: 1.125rem;
  margin-right: 0.3125rem;
}
.back-text[data-v-43b93a3d] {
  font-size: 0.9375rem;
}
.header-title[data-v-43b93a3d] {
  font-size: 1.125rem;
  font-weight: bold;
}
.header-right[data-v-43b93a3d] {
  width: 1.5625rem;
}
.task-status-section[data-v-43b93a3d] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.status-header[data-v-43b93a3d] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.9375rem;
}
.task-id[data-v-43b93a3d] {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
}
.task-status[data-v-43b93a3d] {
  font-size: 0.75rem;
  padding: 0.25rem 0.625rem;
  border-radius: 0.625rem;
}
.status-pending[data-v-43b93a3d] {
  background-color: #fff3cd;
  color: #856404;
}
.status-pickup[data-v-43b93a3d] {
  background-color: #cce5ff;
  color: #004085;
}
.status-delivery[data-v-43b93a3d] {
  background-color: #d4edda;
  color: #155724;
}
.status-completed[data-v-43b93a3d] {
  background-color: #d1ecf1;
  color: #0c5460;
}
.status-progress[data-v-43b93a3d] {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.progress-item[data-v-43b93a3d] {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
}
.progress-item.active[data-v-43b93a3d] {
  opacity: 1;
}
.progress-icon[data-v-43b93a3d] {
  width: 1.5625rem;
  height: 1.5625rem;
  border-radius: 50%;
  background-color: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #999;
  margin-bottom: 0.3125rem;
}
.progress-item.active .progress-icon[data-v-43b93a3d] {
  background-color: #3498db;
  color: white;
}
.progress-text[data-v-43b93a3d] {
  font-size: 0.75rem;
  color: #999;
}
.progress-item.active .progress-text[data-v-43b93a3d] {
  color: #333;
  font-weight: bold;
}
.progress-line[data-v-43b93a3d] {
  flex: 1;
  height: 0.125rem;
  background-color: #ddd;
  margin: 0 0.3125rem;
}
.progress-line.active[data-v-43b93a3d] {
  background-color: #3498db;
}
.section[data-v-43b93a3d] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.section-header[data-v-43b93a3d] {
  padding: 0.625rem 0.9375rem;
  border-bottom: 0.03125rem solid #eee;
}
.section-title[data-v-43b93a3d] {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
}
.section-content[data-v-43b93a3d] {
  padding: 0.625rem 0.9375rem;
}
.info-item[data-v-43b93a3d] {
  display: flex;
  margin-bottom: 0.625rem;
}
.info-item[data-v-43b93a3d]:last-child {
  margin-bottom: 0;
}
.info-label[data-v-43b93a3d] {
  width: 5.625rem;
  font-size: 0.875rem;
  color: #666;
}
.info-value[data-v-43b93a3d] {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
}
.phone-number[data-v-43b93a3d] {
  color: #3498db;
}
.action-buttons[data-v-43b93a3d] {
  display: flex;
  flex-direction: column;
  padding: 0.625rem;
  gap: 0.625rem;
}
.action-btn[data-v-43b93a3d] {
  padding: 0.625rem;
  border-radius: 0.3125rem;
  font-size: 0.9375rem;
  border: none;
  font-weight: bold;
}
.primary[data-v-43b93a3d] {
  background-color: #3498db;
  color: white;
}
.secondary[data-v-43b93a3d] {
  background-color: white;
  color: #333;
  border: 0.03125rem solid #ddd;
}
