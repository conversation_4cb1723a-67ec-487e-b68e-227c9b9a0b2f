
.statistics-container[data-v-6e199430] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  padding-bottom: 3.75rem;
  box-sizing: border-box;
}
.status-bar[data-v-6e199430] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: #3498db;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}
.header[data-v-6e199430] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: #3498db;
  color: white;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}
.header-title[data-v-6e199430] {
  font-size: 1.125rem;
  font-weight: bold;
}
.date-selector[data-v-6e199430] {
  font-size: 1.125rem;
}
.overview-section[data-v-6e199430] {
  display: flex;
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.overview-item[data-v-6e199430] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.overview-item[data-v-6e199430]:not(:last-child) {
  border-right: 0.03125rem solid #eee;
}
.item-value[data-v-6e199430] {
  font-size: 1.25rem;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 0.3125rem;
}
.item-label[data-v-6e199430] {
  font-size: 0.8125rem;
  color: #666;
}
.chart-section[data-v-6e199430] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.section-header[data-v-6e199430] {
  margin-bottom: 0.9375rem;
}
.section-title[data-v-6e199430] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.chart-container[data-v-6e199430] {
  height: 9.375rem;
}
.chart[data-v-6e199430] {
  display: flex;
  height: 100%;
}
.chart-y-axis[data-v-6e199430] {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 1.875rem;
  padding-right: 0.625rem;
  border-right: 0.03125rem solid #eee;
}
.y-label[data-v-6e199430] {
  font-size: 0.625rem;
  color: #999;
}
.chart-content[data-v-6e199430] {
  flex: 1;
  padding-left: 0.625rem;
}
.chart-bars[data-v-6e199430] {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 100%;
}
.bar-item[data-v-6e199430] {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}
.bar[data-v-6e199430] {
  width: 1.25rem;
  background: linear-gradient(to top, #3498db, #2980b9);
  border-radius: 0.3125rem 0.3125rem 0 0;
  margin-bottom: 0.3125rem;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 0.3125rem;
}
.bar-value[data-v-6e199430] {
  font-size: 0.625rem;
  color: white;
}
.bar-label[data-v-6e199430] {
  font-size: 0.625rem;
  color: #666;
}
.detail-section[data-v-6e199430] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.detail-list[data-v-6e199430] {
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
}
.detail-item[data-v-6e199430] {
  display: flex;
  justify-content: space-between;
  padding: 0.46875rem 0;
  border-bottom: 0.03125rem solid #eee;
}
.detail-item[data-v-6e199430]:last-child {
  border-bottom: none;
}
.detail-label[data-v-6e199430] {
  font-size: 0.875rem;
  color: #666;
}
.detail-value[data-v-6e199430] {
  font-size: 0.875rem;
  color: #333;
  font-weight: 500;
}
.tab-bar[data-v-6e199430] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3.75rem;
  background-color: white;
  display: flex;
  border-top: 0.03125rem solid #eee;
  padding-bottom: env(safe-area-inset-bottom);
}
.tab-item[data-v-6e199430] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}
.tab-item.active[data-v-6e199430] {
  color: #3498db;
}
.tab-icon[data-v-6e199430] {
  font-size: 1.25rem;
  margin-bottom: 0.15625rem;
}
