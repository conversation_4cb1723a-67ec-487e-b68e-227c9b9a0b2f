
.payment-container[data-v-7695f594] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  padding-bottom: 1.25rem;
  box-sizing: border-box;
}
.status-bar[data-v-7695f594] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: #3498db;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}
.header[data-v-7695f594] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: #3498db;
  color: white;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}
.header-left[data-v-7695f594] {
  display: flex;
  align-items: center;
}
.back-icon[data-v-7695f594] {
  font-size: 1.125rem;
  margin-right: 0.3125rem;
}
.back-text[data-v-7695f594] {
  font-size: 0.9375rem;
}
.header-title[data-v-7695f594] {
  font-size: 1.125rem;
  font-weight: bold;
}
.header-right[data-v-7695f594] {
  width: 1.5625rem;
}
.task-info[data-v-7695f594] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.task-header[data-v-7695f594] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.9375rem;
}
.task-id[data-v-7695f594] {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
}
.task-status[data-v-7695f594] {
  font-size: 0.75rem;
  padding: 0.25rem 0.625rem;
  border-radius: 0.625rem;
}
.status-delivery[data-v-7695f594] {
  background-color: #d4edda;
  color: #155724;
}
.task-details[data-v-7695f594] {
  display: flex;
  flex-direction: column;
  gap: 0.46875rem;
}
.detail-item[data-v-7695f594] {
  display: flex;
}
.label[data-v-7695f594] {
  width: 5rem;
  font-size: 0.875rem;
  color: #666;
}
.value[data-v-7695f594] {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
}
.phone-number[data-v-7695f594] {
  color: #3498db;
}
.payment-section[data-v-7695f594] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.section-title[data-v-7695f594] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.9375rem;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid #eee;
}
.fee-details[data-v-7695f594] {
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
}
.fee-item[data-v-7695f594] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fee-label[data-v-7695f594] {
  font-size: 0.875rem;
  color: #666;
}
.fee-value[data-v-7695f594] {
  font-size: 0.875rem;
  color: #333;
  font-weight: 500;
}
.fee-divider[data-v-7695f594] {
  height: 0.03125rem;
  background-color: #eee;
  margin: 0.3125rem 0;
}
.total[data-v-7695f594] {
  font-weight: bold;
}
.total-value[data-v-7695f594] {
  color: #e74c3c;
  font-size: 1rem;
}
.payment-methods[data-v-7695f594] {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.625rem;
}
.method-item[data-v-7695f594] {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.9375rem 0.625rem;
  border: 0.03125rem solid #ddd;
  border-radius: 0.3125rem;
  font-size: 0.875rem;
  color: #666;
}
.method-item.active[data-v-7695f594] {
  border-color: #3498db;
  color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
}
.method-icon[data-v-7695f594] {
  font-size: 1.5rem;
  margin-bottom: 0.3125rem;
}
.action-buttons[data-v-7695f594] {
  padding: 0.625rem;
  margin-top: auto;
}
.action-btn[data-v-7695f594] {
  width: 100%;
  height: 2.5rem;
  border-radius: 0.3125rem;
  font-size: 1rem;
  font-weight: bold;
  border: none;
}
.action-btn.primary[data-v-7695f594] {
  background-color: #3498db;
  color: white;
}
.action-btn.primary[data-v-7695f594]:hover:not(:disabled) {
  background-color: #2980b9;
}
.action-btn.secondary[data-v-7695f594] {
  background-color: #eee;
  color: #666;
}
.action-btn.secondary[data-v-7695f594]:hover:not(:disabled) {
  background-color: #ddd;
}
.action-btn[data-v-7695f594]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
