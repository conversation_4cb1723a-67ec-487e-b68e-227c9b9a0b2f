
/* 页面内容容器 */
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 1.25rem 3.75rem;
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
  background: linear-gradient(135deg, var(--primary-color, #3498db), var(--secondary-color, #2c3e50));
  position: relative;
  /* 使用安全区域适配，避免与状态栏重叠 */
  padding-top: max(env(safe-area-inset-top), 1.25rem);
}

/* 状态栏样式 */
.status-bar {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: transparent;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

/* 装饰元素 */
.content::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  transform: rotate(30deg);
  z-index: 0;
}

/* Logo样式 */
.logo {
  height: 6.25rem;
  width: 6.25rem;
  margin: 1.25rem;
  margin-top: 1.875rem;
  border-radius: 0.9375rem;
  box-shadow: 0 0.3125rem 0.9375rem rgba(0, 0, 0, 0.2);
  background-color: var(--bg-white, #ffffff);
  padding: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color, #3498db);
  font-size: 2.5rem;
  font-weight: bold;
  position: relative;
  z-index: 1;
}

/* 文本区域 */
.text-area {
  display: flex;
  justify-content: center;
  margin: 1.25rem;
  position: relative;
  z-index: 1;
}

/* 页面标题 */
.title {
  font-size: 1.125rem;
  color: var(--text-white, #ffffff);
  font-weight: bold;
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.2);
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media screen and (max-width: 768px) {
.content {
    padding: 0.625rem 0.625rem 3.125rem;
}
.logo {
    height: 4.6875rem;
    width: 4.6875rem;
}
.title {
    font-size: 1rem;
}
}
