
.task-container[data-v-b071c5a1] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  padding-bottom: 3.75rem;
  box-sizing: border-box;
}
.status-bar[data-v-b071c5a1] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: #3498db;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}
.header[data-v-b071c5a1] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: #3498db;
  color: white;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}
.header-title[data-v-b071c5a1] {
  font-size: 1.125rem;
  font-weight: bold;
}
.refresh-btn[data-v-b071c5a1] {
  font-size: 0.875rem;
  padding: 0.3125rem 0.625rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.3125rem;
}
.filter-container[data-v-b071c5a1] {
  display: flex;
  background-color: white;
  padding: 0.625rem;
  box-shadow: 0 0.0625rem 0.15625rem rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 9;
}
.filter-item[data-v-b071c5a1] {
  flex: 1;
  text-align: center;
  padding: 0.46875rem 0;
  font-size: 0.875rem;
  color: #666;
  border-radius: 0.3125rem;
  margin: 0 0.3125rem;
}
.filter-item.active[data-v-b071c5a1] {
  background-color: #3498db;
  color: white;
  font-weight: bold;
}
.task-list[data-v-b071c5a1] {
  flex: 1;
  padding: 0.625rem;
  overflow-y: auto;
}
.task-item[data-v-b071c5a1] {
  background-color: white;
  border-radius: 0.46875rem;
  padding: 0.78125rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.task-header[data-v-b071c5a1] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.625rem;
  border-bottom: 0.03125rem solid #eee;
  margin-bottom: 0.625rem;
}
.task-id[data-v-b071c5a1] {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
}
.task-status[data-v-b071c5a1] {
  font-size: 0.75rem;
  padding: 0.15625rem 0.46875rem;
  border-radius: 0.3125rem;
}
.status-pending[data-v-b071c5a1] {
  background-color: #fff3cd;
  color: #856404;
}
.status-pickup[data-v-b071c5a1] {
  background-color: #cce5ff;
  color: #004085;
}
.status-delivery[data-v-b071c5a1] {
  background-color: #d4edda;
  color: #155724;
}
.task-content[data-v-b071c5a1] {
  display: flex;
  flex-direction: column;
  gap: 0.46875rem;
}
.label[data-v-b071c5a1] {
  font-size: 0.75rem;
  color: #999;
  margin-right: 0.3125rem;
}
.address[data-v-b071c5a1], .time[data-v-b071c5a1], .customer[data-v-b071c5a1] {
  font-size: 0.8125rem;
  color: #333;
}
.task-footer[data-v-b071c5a1] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.625rem;
  padding-top: 0.625rem;
  border-top: 0.03125rem solid #eee;
}
.distance[data-v-b071c5a1] {
  font-size: 0.75rem;
  color: #999;
}
.action-btn[data-v-b071c5a1] {
  font-size: 0.8125rem;
  padding: 0.3125rem 0.78125rem;
  border-radius: 0.3125rem;
  color: white;
  border: none;
}
.action-start[data-v-b071c5a1] {
  background-color: #3498db;
}
.action-pickup[data-v-b071c5a1] {
  background-color: #28a745;
}
.action-delivery[data-v-b071c5a1] {
  background-color: #ffc107;
  color: #333;
}
.empty-state[data-v-b071c5a1] {
  text-align: center;
  padding: 3.125rem 0;
}
.empty-text[data-v-b071c5a1] {
  font-size: 0.9375rem;
  color: #999;
}
.tab-bar[data-v-b071c5a1] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3.75rem;
  background-color: white;
  display: flex;
  border-top: 0.03125rem solid #eee;
  padding-bottom: env(safe-area-inset-bottom);
}
.tab-item[data-v-b071c5a1] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}
.tab-item.active[data-v-b071c5a1] {
  color: #3498db;
}
.tab-icon[data-v-b071c5a1] {
  font-size: 1.25rem;
  margin-bottom: 0.15625rem;
}


.statistics-container[data-v-6e199430] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  padding-bottom: 3.75rem;
  box-sizing: border-box;
}
.status-bar[data-v-6e199430] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: #3498db;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}
.header[data-v-6e199430] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: #3498db;
  color: white;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}
.header-title[data-v-6e199430] {
  font-size: 1.125rem;
  font-weight: bold;
}
.date-selector[data-v-6e199430] {
  font-size: 1.125rem;
}
.overview-section[data-v-6e199430] {
  display: flex;
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.overview-item[data-v-6e199430] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.overview-item[data-v-6e199430]:not(:last-child) {
  border-right: 0.03125rem solid #eee;
}
.item-value[data-v-6e199430] {
  font-size: 1.25rem;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 0.3125rem;
}
.item-label[data-v-6e199430] {
  font-size: 0.8125rem;
  color: #666;
}
.chart-section[data-v-6e199430] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.section-header[data-v-6e199430] {
  margin-bottom: 0.9375rem;
}
.section-title[data-v-6e199430] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.chart-container[data-v-6e199430] {
  height: 9.375rem;
}
.chart[data-v-6e199430] {
  display: flex;
  height: 100%;
}
.chart-y-axis[data-v-6e199430] {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 1.875rem;
  padding-right: 0.625rem;
  border-right: 0.03125rem solid #eee;
}
.y-label[data-v-6e199430] {
  font-size: 0.625rem;
  color: #999;
}
.chart-content[data-v-6e199430] {
  flex: 1;
  padding-left: 0.625rem;
}
.chart-bars[data-v-6e199430] {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 100%;
}
.bar-item[data-v-6e199430] {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}
.bar[data-v-6e199430] {
  width: 1.25rem;
  background: linear-gradient(to top, #3498db, #2980b9);
  border-radius: 0.3125rem 0.3125rem 0 0;
  margin-bottom: 0.3125rem;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 0.3125rem;
}
.bar-value[data-v-6e199430] {
  font-size: 0.625rem;
  color: white;
}
.bar-label[data-v-6e199430] {
  font-size: 0.625rem;
  color: #666;
}
.detail-section[data-v-6e199430] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.detail-list[data-v-6e199430] {
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
}
.detail-item[data-v-6e199430] {
  display: flex;
  justify-content: space-between;
  padding: 0.46875rem 0;
  border-bottom: 0.03125rem solid #eee;
}
.detail-item[data-v-6e199430]:last-child {
  border-bottom: none;
}
.detail-label[data-v-6e199430] {
  font-size: 0.875rem;
  color: #666;
}
.detail-value[data-v-6e199430] {
  font-size: 0.875rem;
  color: #333;
  font-weight: 500;
}
.tab-bar[data-v-6e199430] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3.75rem;
  background-color: white;
  display: flex;
  border-top: 0.03125rem solid #eee;
  padding-bottom: env(safe-area-inset-bottom);
}
.tab-item[data-v-6e199430] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}
.tab-item.active[data-v-6e199430] {
  color: #3498db;
}
.tab-icon[data-v-6e199430] {
  font-size: 1.25rem;
  margin-bottom: 0.15625rem;
}


.profile-container[data-v-201c0da5] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  padding-bottom: 3.75rem;
  box-sizing: border-box;
}
.status-bar[data-v-201c0da5] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: #3498db;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}
.header[data-v-201c0da5] {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: #3498db;
  color: white;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}
.header-title[data-v-201c0da5] {
  font-size: 1.125rem;
  font-weight: bold;
}
.profile-section[data-v-201c0da5] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.user-info[data-v-201c0da5] {
  display: flex;
  align-items: center;
  margin-bottom: 0.9375rem;
}
.avatar[data-v-201c0da5] {
  width: 3.75rem;
  height: 3.75rem;
  border-radius: 50%;
  margin-right: 0.625rem;
  background-color: #eee;
}
.user-details[data-v-201c0da5] {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.3125rem;
}
.user-name[data-v-201c0da5] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.user-id[data-v-201c0da5], .user-phone[data-v-201c0da5] {
  font-size: 0.8125rem;
  color: #666;
}
.edit-btn[data-v-201c0da5] {
  width: 1.875rem;
  height: 1.875rem;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.edit-icon[data-v-201c0da5] {
  font-size: 0.9375rem;
}
.stats-grid[data-v-201c0da5] {
  display: flex;
  border-top: 0.03125rem solid #eee;
  padding-top: 0.9375rem;
}
.stat-item[data-v-201c0da5] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-item[data-v-201c0da5]:not(:last-child) {
  border-right: 0.03125rem solid #eee;
}
.stat-value[data-v-201c0da5] {
  font-size: 1.125rem;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 0.3125rem;
}
.stat-label[data-v-201c0da5] {
  font-size: 0.8125rem;
  color: #666;
}
.menu-section[data-v-201c0da5] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  overflow: hidden;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.menu-item[data-v-201c0da5] {
  display: flex;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #eee;
}
.menu-item[data-v-201c0da5]:last-child {
  border-bottom: none;
}
.menu-icon[data-v-201c0da5] {
  font-size: 1.125rem;
  margin-right: 0.625rem;
  width: 1.5625rem;
}
.menu-text[data-v-201c0da5] {
  flex: 1;
  font-size: 0.9375rem;
  color: #333;
}
.menu-arrow[data-v-201c0da5] {
  color: #999;
  font-size: 1.125rem;
}
.logout-section[data-v-201c0da5] {
  margin: 0.625rem;
}
.logout-btn[data-v-201c0da5] {
  width: 100%;
  padding: 0.625rem;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 0.3125rem;
  font-size: 1rem;
  font-weight: bold;
}
.tab-bar[data-v-201c0da5] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3.75rem;
  background-color: white;
  display: flex;
  border-top: 0.03125rem solid #eee;
  padding-bottom: env(safe-area-inset-bottom);
}
.tab-item[data-v-201c0da5] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}
.tab-item.active[data-v-201c0da5] {
  color: #3498db;
}
.tab-icon[data-v-201c0da5] {
  font-size: 1.25rem;
  margin-bottom: 0.15625rem;
}


.main-container[data-v-d311227b] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  padding-bottom: calc(3.75rem + env(safe-area-inset-bottom));
  box-sizing: border-box;
}
.header[data-v-d311227b] {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: #3498db;
  color: white;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}
.header-title[data-v-d311227b] {
  font-size: 1.125rem;
  font-weight: bold;
}
.swiper-container[data-v-d311227b] {
  flex: 1;
  width: 100%;
}
.page-content[data-v-d311227b] {
  height: 100%;
}
.loading-placeholder[data-v-d311227b] {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 0.875rem;
  color: #999;
}
.tab-bar[data-v-d311227b] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3.75rem;
  background-color: white;
  display: flex;
  border-top: 0.03125rem solid #eee;
  padding-bottom: env(safe-area-inset-bottom);
}
.tab-item[data-v-d311227b] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}
.tab-item.active[data-v-d311227b] {
  color: #3498db;
}
.tab-icon[data-v-d311227b] {
  font-size: 1.25rem;
  margin-bottom: 0.15625rem;
}
