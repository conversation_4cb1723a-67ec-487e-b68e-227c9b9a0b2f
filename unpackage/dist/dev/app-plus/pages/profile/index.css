
.profile-container[data-v-201c0da5] {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: max(env(safe-area-inset-top), 1.25rem);
  padding-bottom: 3.75rem;
  box-sizing: border-box;
}
.status-bar[data-v-201c0da5] {
  height: env(safe-area-inset-top);
  width: 100%;
  background-color: #3498db;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}
.header[data-v-201c0da5] {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  background-color: #3498db;
  color: white;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}
.header-title[data-v-201c0da5] {
  font-size: 1.125rem;
  font-weight: bold;
}
.profile-section[data-v-201c0da5] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.user-info[data-v-201c0da5] {
  display: flex;
  align-items: center;
  margin-bottom: 0.9375rem;
}
.avatar[data-v-201c0da5] {
  width: 3.75rem;
  height: 3.75rem;
  border-radius: 50%;
  margin-right: 0.625rem;
  background-color: #eee;
}
.user-details[data-v-201c0da5] {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.3125rem;
}
.user-name[data-v-201c0da5] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.user-id[data-v-201c0da5], .user-phone[data-v-201c0da5] {
  font-size: 0.8125rem;
  color: #666;
}
.edit-btn[data-v-201c0da5] {
  width: 1.875rem;
  height: 1.875rem;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.edit-icon[data-v-201c0da5] {
  font-size: 0.9375rem;
}
.stats-grid[data-v-201c0da5] {
  display: flex;
  border-top: 0.03125rem solid #eee;
  padding-top: 0.9375rem;
}
.stat-item[data-v-201c0da5] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-item[data-v-201c0da5]:not(:last-child) {
  border-right: 0.03125rem solid #eee;
}
.stat-value[data-v-201c0da5] {
  font-size: 1.125rem;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 0.3125rem;
}
.stat-label[data-v-201c0da5] {
  font-size: 0.8125rem;
  color: #666;
}
.menu-section[data-v-201c0da5] {
  background-color: white;
  margin: 0.625rem;
  border-radius: 0.46875rem;
  overflow: hidden;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.menu-item[data-v-201c0da5] {
  display: flex;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #eee;
}
.menu-item[data-v-201c0da5]:last-child {
  border-bottom: none;
}
.menu-icon[data-v-201c0da5] {
  font-size: 1.125rem;
  margin-right: 0.625rem;
  width: 1.5625rem;
}
.menu-text[data-v-201c0da5] {
  flex: 1;
  font-size: 0.9375rem;
  color: #333;
}
.menu-arrow[data-v-201c0da5] {
  color: #999;
  font-size: 1.125rem;
}
.logout-section[data-v-201c0da5] {
  margin: 0.625rem;
}
.logout-btn[data-v-201c0da5] {
  width: 100%;
  padding: 0.625rem;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 0.3125rem;
  font-size: 1rem;
  font-weight: bold;
}
.tab-bar[data-v-201c0da5] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3.75rem;
  background-color: white;
  display: flex;
  border-top: 0.03125rem solid #eee;
  padding-bottom: env(safe-area-inset-bottom);
}
.tab-item[data-v-201c0da5] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}
.tab-item.active[data-v-201c0da5] {
  color: #3498db;
}
.tab-icon[data-v-201c0da5] {
  font-size: 1.25rem;
  margin-bottom: 0.15625rem;
}
