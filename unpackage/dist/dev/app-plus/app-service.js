if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global = uni.requireGlobal();
  ArrayBuffer = global.ArrayBuffer;
  Int8Array = global.Int8Array;
  Uint8Array = global.Uint8Array;
  Uint8ClampedArray = global.Uint8ClampedArray;
  Int16Array = global.Int16Array;
  Uint16Array = global.Uint16Array;
  Int32Array = global.Int32Array;
  Uint32Array = global.Uint32Array;
  Float32Array = global.Float32Array;
  Float64Array = global.Float64Array;
  BigInt64Array = global.BigInt64Array;
  BigUint64Array = global.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  const _imports_0 = "/static/logo.png";
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const __default__ = {
    // 主题切换事件处理
    methods: {}
  };
  const _sfc_main$c = /* @__PURE__ */ Object.assign(__default__, {
    __name: "index",
    setup(__props, { expose: __expose }) {
      __expose();
      const title = vue.ref("首页");
      vue.onMounted(() => {
        setTimeout(() => {
          uni.navigateTo({
            url: "/pages/login/login"
          });
        }, 5e3);
      });
      const __returned__ = { title, ref: vue.ref, onMounted: vue.onMounted };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$b(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock(
      vue.Fragment,
      null,
      [
        vue.createCommentVNode(" 首页容器 "),
        vue.createElementVNode("view", { class: "content" }, [
          vue.createCommentVNode(" 状态栏占位 "),
          vue.createElementVNode("view", { class: "status-bar" }),
          vue.createCommentVNode(" 应用Logo "),
          vue.createElementVNode("image", {
            class: "logo",
            src: _imports_0
          }),
          vue.createCommentVNode(" 标题区域 "),
          vue.createElementVNode("view", { class: "text-area" }, [
            vue.createElementVNode(
              "text",
              { class: "title" },
              vue.toDisplayString($setup.title),
              1
              /* TEXT */
            )
          ])
        ])
      ],
      2112
      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
    );
  }
  const PagesIndexIndex = /* @__PURE__ */ _export_sfc(_sfc_main$c, [["render", _sfc_render$b], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/index/index.vue"]]);
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  class LoginService {
    /**
     * 通用的请求处理方法
     * 根据环境决定使用真实请求还是模拟请求
     * @param {Object} options - 请求配置参数
     * @returns {Promise} 请求结果Promise
     */
    static request(options) {
      {
        return this.mockRequest(options);
      }
    }
    /**
     * 模拟请求处理方法（测试模式）
     * 模拟网络请求，用于开发和测试环境
     * @param {Object} options - 请求配置参数
     * @returns {Promise} 模拟请求结果Promise
     */
    static mockRequest(options) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (options.url === "/api/login/account" && options.method === "POST") {
            const { username, password } = options.data;
            if (username === "test" && password === "123456") {
              resolve({
                success: true,
                message: "登录成功",
                data: {
                  userId: "1001",
                  username,
                  token: "fake_token_string"
                }
              });
            } else {
              reject({
                success: false,
                message: "用户名或密码错误"
              });
            }
          } else if (options.url === "/api/login/phone" && options.method === "POST") {
            const { phoneNumber, code } = options.data;
            if (phoneNumber === "***********" && code === "123456") {
              resolve({
                success: true,
                message: "登录成功",
                data: {
                  userId: "1002",
                  phoneNumber,
                  token: "fake_token_string"
                }
              });
            } else {
              reject({
                success: false,
                message: "手机号或验证码错误"
              });
            }
          } else if (options.url === "/api/login/send-code" && options.method === "POST") {
            const { phoneNumber } = options.data;
            if (/^1\d{10}$/.test(phoneNumber)) {
              resolve({
                success: true,
                message: "验证码已发送"
              });
            } else {
              reject({
                success: false,
                message: "手机号格式不正确"
              });
            }
          } else {
            resolve({
              success: true,
              message: "请求成功"
            });
          }
        }, 1e3);
      });
    }
    /**
     * 账号密码登录
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Promise} 登录结果Promise
     */
    static accountLogin(username, password) {
      return this.request({
        url: "/api/login/account",
        method: "POST",
        data: {
          username,
          password
        }
      });
    }
    /**
     * 手机号验证码登录
     * @param {string} phoneNumber - 手机号
     * @param {string} code - 验证码
     * @returns {Promise} 登录结果Promise
     */
    static phoneLogin(phoneNumber, code) {
      return this.request({
        url: "/api/login/phone",
        method: "POST",
        data: {
          phoneNumber,
          code
        }
      });
    }
    /**
     * 发送验证码
     * @param {string} phoneNumber - 手机号
     * @returns {Promise} 发送结果Promise
     */
    static sendVerificationCode(phoneNumber) {
      return this.request({
        url: "/api/login/send-code",
        method: "POST",
        data: {
          phoneNumber
        }
      });
    }
  }
  class ValidationUtil {
    /**
     * 验证手机号格式
     * @param {string} phoneNumber - 手机号
     * @returns {boolean} 是否有效
     */
    static isValidPhoneNumber(phoneNumber) {
      return /^1\d{10}$/.test(phoneNumber);
    }
    /**
     * 验证密码强度
     * @param {string} password - 密码
     * @returns {boolean} 是否有效
     */
    static isValidPassword(password) {
      {
        return password.length >= 1;
      }
    }
    /**
     * 验证验证码格式
     * @param {string} code - 验证码
     * @returns {boolean} 是否有效
     */
    static isValidVerificationCode(code) {
      {
        return code.length === 6;
      }
    }
    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱
     * @returns {boolean} 是否有效
     */
    static isValidEmail(email) {
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }
    /**
     * 验证账号密码表单
     * @param {Object} formData - 表单数据 {username, password}
     * @returns {Object} 验证结果 {isValid, errors}
     */
    static validateAccountForm(formData) {
      const errors = [];
      if (!formData.username) {
        errors.push("请输入用户名");
      }
      if (!formData.password) {
        errors.push("请输入密码");
      } else if (!this.isValidPassword(formData.password)) {
        errors.push("密码需包含大小写字母和数字，长度6-20");
      }
      return {
        isValid: errors.length === 0,
        errors
      };
    }
    /**
     * 验证手机号表单
     * @param {Object} formData - 表单数据 {phoneNumber, code}
     * @returns {Object} 验证结果 {isValid, errors}
     */
    static validatePhoneForm(formData) {
      const errors = [];
      if (!formData.phoneNumber) {
        errors.push("请输入手机号");
      } else if (!this.isValidPhoneNumber(formData.phoneNumber)) {
        errors.push("手机号格式不正确");
      }
      if (!formData.code) {
        errors.push("请输入验证码");
      } else if (!this.isValidVerificationCode(formData.code)) {
        errors.push("验证码格式不正确");
      }
      return {
        isValid: errors.length === 0,
        errors
      };
    }
  }
  const _sfc_main$b = {
    // 页面数据
    data() {
      return {
        activeTab: "account",
        // 当前激活的登录标签页 ('account' 或 'phone')
        showPassword: false,
        // 密码是否可见
        countdown: 0,
        // 验证码倒计时
        // 账号登录表单数据
        account: {
          username: "",
          password: ""
        },
        // 手机号登录表单数据
        phone: {
          phoneNumber: "",
          code: ""
        },
        accountError: "",
        // 账号登录错误信息
        phoneError: "",
        // 手机号登录错误信息
        animationData: {}
        // 登录动画数据
      };
    },
    // 计算属性
    computed: {
      // 验证账号登录表单是否有效
      isValidAccountLogin() {
        return this.account.username && this.account.password;
      },
      // 验证手机号登录表单是否有效
      isValidPhoneLogin() {
        return /^1[3-9]\d{9}$/.test(this.phone.phoneNumber) && /^\d{6}$/.test(this.phone.code);
      }
    },
    /**
       * 页面加载时执行
       */
    created() {
      try {
        themeManager.init();
        this.currentTheme = themeManager.getCurrentTheme();
      } catch (e) {
        formatAppLog("error", "at pages/login/login.vue:170", "Theme initialization failed:", e);
      }
    },
    // 页面方法
    methods: {
      /**
       * 切换到账号登录标签页 - 清空手机号登录表单
       */
      switchToAccount() {
        this.phone = {
          phoneNumber: "",
          code: ""
        };
        this.phoneError = "";
        this.activeTab = "account";
      },
      /**
       * 切换到手机号登录标签页 - 清空账号登录表单
       */
      switchToPhone() {
        this.account = {
          username: "",
          password: ""
        };
        this.accountError = "";
        this.activeTab = "phone";
      },
      /**
       * 切换密码可见性
       */
      togglePasswordVisibility() {
        this.showPassword = !this.showPassword;
      },
      /**
       * 处理账号登录
       */
      async handleAccountLogin() {
        const validation = ValidationUtil.validateAccountForm(this.account);
        if (!validation.isValid) {
          this.accountError = validation.errors[0];
          return;
        }
        this.accountError = "";
        try {
          uni.showLoading({
            title: "登录中..."
          });
          const result = await LoginService.accountLogin(
            this.account.username,
            this.account.password
          );
          uni.hideLoading();
          uni.showToast({
            title: result.message,
            icon: "success"
          });
          const animation = uni.createAnimation({
            duration: 500,
            timingFunction: "ease"
          });
          animation.scale(0.9).opacity(0.5).step();
          this.animationData = animation.export();
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/main/index",
              animationType: "slide-in-right",
              animationDuration: 300
            });
          }, 800);
        } catch (error) {
          uni.hideLoading();
          this.accountError = error.message;
        }
      },
      /**
         * 获取验证码
         */
      async getCode() {
        if (!this.phone.phoneNumber) {
          this.phoneError = "请输入手机号";
          return;
        }
        if (!/^1\d{10}$/.test(this.phone.phoneNumber)) {
          this.phoneError = "请输入正确的手机号";
          return;
        }
        this.phoneError = "";
        try {
          uni.showLoading({
            title: "发送中..."
          });
          const result = await LoginService.sendVerificationCode(this.phone.phoneNumber);
          uni.hideLoading();
          uni.showToast({
            title: result.message,
            icon: "success"
          });
          this.startCountdown();
        } catch (error) {
          uni.hideLoading();
          this.phoneError = error.message;
        }
      },
      /**
       * 启动验证码倒计时
       */
      startCountdown() {
        this.countdown = 60;
        const timer = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown--;
          } else {
            clearInterval(timer);
          }
        }, 1e3);
      },
      /**
       * 处理手机号登录
       */
      async handlePhoneLogin() {
        const validation = ValidationUtil.validatePhoneForm(this.phone);
        if (!validation.isValid) {
          this.phoneError = validation.errors[0];
          return;
        }
        this.phoneError = "";
        try {
          uni.showLoading({
            title: "登录中..."
          });
          const result = await LoginService.phoneLogin(
            this.phone.phoneNumber,
            this.phone.code
          );
          uni.hideLoading();
          uni.showToast({
            title: result.message,
            icon: "success"
          });
          const animation = uni.createAnimation({
            duration: 500,
            timingFunction: "ease"
          });
          animation.scale(0.9).opacity(0.5).step();
          this.animationData = animation.export();
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/main/index",
              animationType: "slide-in-right",
              animationDuration: 300
            });
          }, 800);
        } catch (error) {
          uni.hideLoading();
          this.phoneError = error.message;
        }
      }
      // 移除原有的onThemeChange方法，使用新的switchTheme方法
    }
  };
  function _sfc_render$a(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock(
      vue.Fragment,
      null,
      [
        vue.createCommentVNode(" 登录页面容器 "),
        vue.createElementVNode(
          "view",
          {
            class: "login-container",
            ref: "loginContainer"
          },
          [
            vue.createCommentVNode(" 状态栏占位 "),
            vue.createElementVNode("view", { class: "status-bar" }),
            vue.createCommentVNode(" 登录页面头部 "),
            vue.createElementVNode("view", { class: "login-header" }, [
              vue.createCommentVNode(" 应用Logo "),
              vue.createElementVNode("image", {
                class: "logo",
                src: _imports_0
              }),
              vue.createCommentVNode(" 应用标题 "),
              vue.createElementVNode("text", { class: "app-title" }, "快递员端")
            ]),
            vue.createCommentVNode(" 登录方式切换标签 "),
            vue.createElementVNode("view", { class: "login-tabs" }, [
              vue.createElementVNode(
                "view",
                {
                  class: vue.normalizeClass([{ active: $data.activeTab === "account" }, "tab-item"]),
                  onClick: _cache[0] || (_cache[0] = (...args) => $options.switchToAccount && $options.switchToAccount(...args))
                },
                " 账号登录 ",
                2
                /* CLASS */
              ),
              vue.createElementVNode(
                "view",
                {
                  class: vue.normalizeClass([{ active: $data.activeTab === "phone" }, "tab-item"]),
                  onClick: _cache[1] || (_cache[1] = (...args) => $options.switchToPhone && $options.switchToPhone(...args))
                },
                " 手机号登录 ",
                2
                /* CLASS */
              )
            ]),
            vue.createCommentVNode(" 账号登录表单 "),
            $data.activeTab === "account" ? (vue.openBlock(), vue.createElementBlock("view", {
              key: 0,
              class: "login-form"
            }, [
              vue.createCommentVNode(" 用户名输入框组 "),
              vue.createElementVNode("view", { class: "input-group" }, [
                vue.withDirectives(vue.createElementVNode(
                  "input",
                  {
                    type: "text",
                    "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $data.account.username = $event),
                    placeholder: "请输入账号",
                    class: "login-input"
                  },
                  null,
                  512
                  /* NEED_PATCH */
                ), [
                  [vue.vModelText, $data.account.username]
                ])
              ]),
              vue.createCommentVNode(" 密码输入框组 "),
              vue.createElementVNode("view", { class: "input-group password-group" }, [
                vue.withDirectives(vue.createElementVNode("input", {
                  type: $data.showPassword ? "text" : "password",
                  "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $data.account.password = $event),
                  placeholder: "请输入密码",
                  class: "login-input",
                  onKeyup: _cache[4] || (_cache[4] = vue.withKeys((...args) => $options.handleAccountLogin && $options.handleAccountLogin(...args), ["enter"]))
                }, null, 40, ["type"]), [
                  [vue.vModelDynamic, $data.account.password]
                ]),
                vue.createCommentVNode(" 密码可见性切换图标 "),
                vue.createElementVNode(
                  "view",
                  {
                    onClick: _cache[5] || (_cache[5] = (...args) => $options.togglePasswordVisibility && $options.togglePasswordVisibility(...args)),
                    class: vue.normalizeClass(["eye-icon", { closed: !$data.showPassword }])
                  },
                  null,
                  2
                  /* CLASS */
                )
              ]),
              vue.createCommentVNode(" 登录按钮 "),
              vue.createElementVNode("button", {
                disabled: !$options.isValidAccountLogin,
                onClick: _cache[6] || (_cache[6] = (...args) => $options.handleAccountLogin && $options.handleAccountLogin(...args)),
                class: "login-button"
              }, " 登录 ", 8, ["disabled"]),
              vue.createCommentVNode(" 账号登录错误信息 "),
              $data.accountError ? (vue.openBlock(), vue.createElementBlock(
                "text",
                {
                  key: 0,
                  class: "error-message"
                },
                vue.toDisplayString($data.accountError),
                1
                /* TEXT */
              )) : vue.createCommentVNode("v-if", true)
            ])) : (vue.openBlock(), vue.createElementBlock(
              vue.Fragment,
              { key: 1 },
              [
                vue.createCommentVNode(" 手机号登录表单 "),
                vue.createElementVNode("view", { class: "login-form" }, [
                  vue.createCommentVNode(" 手机号输入框组 "),
                  vue.createElementVNode("view", { class: "input-group" }, [
                    vue.withDirectives(vue.createElementVNode(
                      "input",
                      {
                        type: "text",
                        "onUpdate:modelValue": _cache[7] || (_cache[7] = ($event) => $data.phone.phoneNumber = $event),
                        placeholder: "请输入手机号",
                        maxlength: "11",
                        class: "login-input"
                      },
                      null,
                      512
                      /* NEED_PATCH */
                    ), [
                      [vue.vModelText, $data.phone.phoneNumber]
                    ])
                  ]),
                  vue.createCommentVNode(" 验证码输入框组 "),
                  vue.createElementVNode("view", { class: "input-group verification-group" }, [
                    vue.withDirectives(vue.createElementVNode(
                      "input",
                      {
                        type: "text",
                        "onUpdate:modelValue": _cache[8] || (_cache[8] = ($event) => $data.phone.code = $event),
                        placeholder: "请输入验证码",
                        maxlength: "6",
                        class: "login-input verification-input"
                      },
                      null,
                      512
                      /* NEED_PATCH */
                    ), [
                      [vue.vModelText, $data.phone.code]
                    ]),
                    vue.createCommentVNode(" 获取验证码按钮 "),
                    vue.createElementVNode("button", {
                      disabled: $data.countdown > 0,
                      onClick: _cache[9] || (_cache[9] = (...args) => $options.getCode && $options.getCode(...args)),
                      class: "verification-button"
                    }, vue.toDisplayString($data.countdown > 0 ? `${$data.countdown}秒后重发` : "获取验证码"), 9, ["disabled"])
                  ]),
                  vue.createCommentVNode(" 登录按钮 "),
                  vue.createElementVNode("button", {
                    disabled: !$options.isValidPhoneLogin,
                    onClick: _cache[10] || (_cache[10] = (...args) => $options.handlePhoneLogin && $options.handlePhoneLogin(...args)),
                    class: "login-button"
                  }, " 登录 ", 8, ["disabled"]),
                  vue.createCommentVNode(" 手机号登录错误信息 "),
                  $data.phoneError ? (vue.openBlock(), vue.createElementBlock(
                    "text",
                    {
                      key: 0,
                      class: "error-message"
                    },
                    vue.toDisplayString($data.phoneError),
                    1
                    /* TEXT */
                  )) : vue.createCommentVNode("v-if", true)
                ])
              ],
              2112
              /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
            ))
          ],
          512
          /* NEED_PATCH */
        )
      ],
      2112
      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
    );
  }
  const PagesLoginLogin = /* @__PURE__ */ _export_sfc(_sfc_main$b, [["render", _sfc_render$a], ["__scopeId", "data-v-e4e4508d"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/login/login.vue"]]);
  const _sfc_main$a = {
    data() {
      return {
        currentFilter: "all",
        tasks: [
          {
            id: "T20230401001",
            status: "pending",
            statusText: "待处理",
            statusClass: "status-pending",
            deliveryAddress: "北京市朝阳区某某街道某某小区1号楼101室",
            createTime: "2023-04-01 10:30",
            customerName: "张先生",
            customerPhone: "138****8888",
            distance: "2.5",
            actionText: "开始处理",
            actionClass: "action-start"
          },
          {
            id: "T20230401002",
            status: "pickup",
            statusText: "待取件",
            statusClass: "status-pickup",
            deliveryAddress: "北京市海淀区某某大厦202室",
            createTime: "2023-04-01 09:15",
            customerName: "李女士",
            customerPhone: "139****9999",
            distance: "5.2",
            actionText: "去取件",
            actionClass: "action-pickup"
          },
          {
            id: "T20230401003",
            status: "delivery",
            statusText: "待派送",
            statusClass: "status-delivery",
            deliveryAddress: "北京市西城区某某胡同3号院",
            createTime: "2023-04-01 08:45",
            customerName: "王先生",
            customerPhone: "137****7777",
            distance: "3.8",
            actionText: "去派送",
            actionClass: "action-delivery"
          }
        ]
      };
    },
    computed: {
      filteredTasks() {
        if (this.currentFilter === "all") {
          return this.tasks;
        }
        return this.tasks.filter((task) => task.status === this.currentFilter);
      }
    },
    methods: {
      filterTasks(filter) {
        this.currentFilter = filter;
      },
      refreshTasks() {
        uni.showToast({
          title: "刷新中...",
          icon: "loading",
          duration: 1e3
        });
        setTimeout(() => {
          uni.showToast({
            title: "刷新成功",
            icon: "success"
          });
        }, 1e3);
      },
      goToTaskDetail(id) {
        uni.navigateTo({
          url: `/pages/task/detail?id=${id}`,
          animationType: "slide-in-right",
          animationDuration: 300
        });
      }
    }
  };
  function _sfc_render$9(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "task-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("text", { class: "header-title" }, "任务列表"),
        vue.createElementVNode("view", { class: "header-actions" }, [
          vue.createElementVNode("text", {
            class: "refresh-btn",
            onClick: _cache[0] || (_cache[0] = (...args) => $options.refreshTasks && $options.refreshTasks(...args))
          }, "刷新")
        ])
      ]),
      vue.createCommentVNode(" 任务筛选 "),
      vue.createElementVNode("view", { class: "filter-container" }, [
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["filter-item", { active: $data.currentFilter === "all" }]),
            onClick: _cache[1] || (_cache[1] = ($event) => $options.filterTasks("all"))
          },
          " 全部 ",
          2
          /* CLASS */
        ),
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["filter-item", { active: $data.currentFilter === "pending" }]),
            onClick: _cache[2] || (_cache[2] = ($event) => $options.filterTasks("pending"))
          },
          " 待处理 ",
          2
          /* CLASS */
        ),
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["filter-item", { active: $data.currentFilter === "pickup" }]),
            onClick: _cache[3] || (_cache[3] = ($event) => $options.filterTasks("pickup"))
          },
          " 待取件 ",
          2
          /* CLASS */
        ),
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["filter-item", { active: $data.currentFilter === "delivery" }]),
            onClick: _cache[4] || (_cache[4] = ($event) => $options.filterTasks("delivery"))
          },
          " 待派送 ",
          2
          /* CLASS */
        )
      ]),
      vue.createCommentVNode(" 任务列表 "),
      vue.createElementVNode("scroll-view", {
        class: "task-list",
        "scroll-y": "true"
      }, [
        (vue.openBlock(true), vue.createElementBlock(
          vue.Fragment,
          null,
          vue.renderList($options.filteredTasks, (task) => {
            return vue.openBlock(), vue.createElementBlock("view", {
              class: "task-item",
              key: task.id,
              onClick: ($event) => $options.goToTaskDetail(task.id)
            }, [
              vue.createElementVNode("view", { class: "task-header" }, [
                vue.createElementVNode(
                  "text",
                  { class: "task-id" },
                  "任务单号: " + vue.toDisplayString(task.id),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "text",
                  {
                    class: vue.normalizeClass(["task-status", task.statusClass])
                  },
                  vue.toDisplayString(task.statusText),
                  3
                  /* TEXT, CLASS */
                )
              ]),
              vue.createElementVNode("view", { class: "task-content" }, [
                vue.createElementVNode("view", { class: "task-address" }, [
                  vue.createElementVNode("text", { class: "label" }, "收货地址:"),
                  vue.createElementVNode(
                    "text",
                    { class: "address" },
                    vue.toDisplayString(task.deliveryAddress),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "task-time" }, [
                  vue.createElementVNode("text", { class: "label" }, "下单时间:"),
                  vue.createElementVNode(
                    "text",
                    { class: "time" },
                    vue.toDisplayString(task.createTime),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "task-customer" }, [
                  vue.createElementVNode("text", { class: "label" }, "收货人:"),
                  vue.createElementVNode(
                    "text",
                    { class: "customer" },
                    vue.toDisplayString(task.customerName) + " " + vue.toDisplayString(task.customerPhone),
                    1
                    /* TEXT */
                  )
                ])
              ]),
              vue.createElementVNode("view", { class: "task-footer" }, [
                vue.createElementVNode(
                  "text",
                  { class: "distance" },
                  vue.toDisplayString(task.distance) + "km",
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "button",
                  {
                    class: vue.normalizeClass(["action-btn", task.actionClass])
                  },
                  vue.toDisplayString(task.actionText),
                  3
                  /* TEXT, CLASS */
                )
              ])
            ], 8, ["onClick"]);
          }),
          128
          /* KEYED_FRAGMENT */
        )),
        vue.createCommentVNode(" 空状态 "),
        $options.filteredTasks.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "empty-state"
        }, [
          vue.createElementVNode("text", { class: "empty-text" }, "暂无任务")
        ])) : vue.createCommentVNode("v-if", true)
      ])
    ]);
  }
  const PagesTaskList = /* @__PURE__ */ _export_sfc(_sfc_main$a, [["render", _sfc_render$9], ["__scopeId", "data-v-b071c5a1"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/task/list.vue"]]);
  const _sfc_main$9 = {
    data() {
      return {
        statistics: {
          totalTasks: 128,
          completedTasks: 115,
          income: 860,
          pendingPickup: 3,
          pendingDelivery: 5,
          todayCompleted: 28,
          exceptionTasks: 2,
          avgCompletionTime: 45
        },
        chartData: [
          { label: "周一", value: 18, height: "60%" },
          { label: "周二", value: 22, height: "73%" },
          { label: "周三", value: 15, height: "50%" },
          { label: "周四", value: 25, height: "83%" },
          { label: "周五", value: 30, height: "100%" },
          { label: "周六", value: 12, height: "40%" },
          { label: "周日", value: 8, height: "27%" }
        ]
      };
    },
    methods: {
      selectDateRange() {
        uni.showToast({
          title: "日期选择功能开发中",
          icon: "none"
        });
      },
      goToPage(url) {
        uni.switchTab({
          url: `/${url}`,
          animationType: "slide-in-right",
          animationDuration: 300
        });
      }
    }
  };
  function _sfc_render$8(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "statistics-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("text", { class: "header-title" }, "工作统计"),
        vue.createElementVNode("view", { class: "header-actions" }, [
          vue.createElementVNode("text", {
            class: "date-selector",
            onClick: _cache[0] || (_cache[0] = (...args) => $options.selectDateRange && $options.selectDateRange(...args))
          }, "📅")
        ])
      ]),
      vue.createCommentVNode(" 统计概览 "),
      vue.createElementVNode("view", { class: "overview-section" }, [
        vue.createElementVNode("view", { class: "overview-item" }, [
          vue.createElementVNode(
            "text",
            { class: "item-value" },
            vue.toDisplayString($data.statistics.totalTasks),
            1
            /* TEXT */
          ),
          vue.createElementVNode("text", { class: "item-label" }, "总任务数")
        ]),
        vue.createElementVNode("view", { class: "overview-item" }, [
          vue.createElementVNode(
            "text",
            { class: "item-value" },
            vue.toDisplayString($data.statistics.completedTasks),
            1
            /* TEXT */
          ),
          vue.createElementVNode("text", { class: "item-label" }, "完成任务")
        ]),
        vue.createElementVNode("view", { class: "overview-item" }, [
          vue.createElementVNode(
            "text",
            { class: "item-value" },
            vue.toDisplayString($data.statistics.income),
            1
            /* TEXT */
          ),
          vue.createElementVNode("text", { class: "item-label" }, "收入(元)")
        ])
      ]),
      vue.createCommentVNode(" 任务统计图表 "),
      vue.createElementVNode("view", { class: "chart-section" }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("text", { class: "section-title" }, "任务完成趋势")
        ]),
        vue.createElementVNode("view", { class: "chart-container" }, [
          vue.createCommentVNode(" 简化的柱状图 "),
          vue.createElementVNode("view", { class: "chart" }, [
            vue.createElementVNode("view", { class: "chart-y-axis" }, [
              vue.createElementVNode("text", { class: "y-label" }, "30"),
              vue.createElementVNode("text", { class: "y-label" }, "20"),
              vue.createElementVNode("text", { class: "y-label" }, "10"),
              vue.createElementVNode("text", { class: "y-label" }, "0")
            ]),
            vue.createElementVNode("view", { class: "chart-content" }, [
              vue.createElementVNode("view", { class: "chart-bars" }, [
                (vue.openBlock(true), vue.createElementBlock(
                  vue.Fragment,
                  null,
                  vue.renderList($data.chartData, (item, index) => {
                    return vue.openBlock(), vue.createElementBlock("view", {
                      class: "bar-item",
                      key: index
                    }, [
                      vue.createElementVNode(
                        "view",
                        {
                          class: "bar",
                          style: vue.normalizeStyle({ height: item.height })
                        },
                        [
                          vue.createElementVNode(
                            "text",
                            { class: "bar-value" },
                            vue.toDisplayString(item.value),
                            1
                            /* TEXT */
                          )
                        ],
                        4
                        /* STYLE */
                      ),
                      vue.createElementVNode(
                        "text",
                        { class: "bar-label" },
                        vue.toDisplayString(item.label),
                        1
                        /* TEXT */
                      )
                    ]);
                  }),
                  128
                  /* KEYED_FRAGMENT */
                ))
              ])
            ])
          ])
        ])
      ]),
      vue.createCommentVNode(" 详细统计 "),
      vue.createElementVNode("view", { class: "detail-section" }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("text", { class: "section-title" }, "详细统计")
        ]),
        vue.createElementVNode("view", { class: "detail-list" }, [
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "待取件"),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($data.statistics.pendingPickup),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "待派送"),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($data.statistics.pendingDelivery),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "今日完成"),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($data.statistics.todayCompleted),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "异常任务"),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($data.statistics.exceptionTasks),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "平均完成时间"),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($data.statistics.avgCompletionTime) + "分钟",
              1
              /* TEXT */
            )
          ])
        ])
      ])
    ]);
  }
  const PagesStatisticsIndex = /* @__PURE__ */ _export_sfc(_sfc_main$9, [["render", _sfc_render$8], ["__scopeId", "data-v-6e199430"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/statistics/index.vue"]]);
  const _sfc_main$8 = {
    data() {
      return {
        userInfo: {
          name: "张三",
          employeeId: "E2023001",
          phone: "138****8888",
          rating: "4.8",
          completedTasks: 1256,
          workDays: 320
        }
      };
    },
    methods: {
      editProfile() {
        uni.showToast({
          title: "编辑资料功能开发中",
          icon: "none"
        });
      },
      goToPage(url) {
        uni.switchTab({
          url: `/${url}`,
          animationType: "slide-in-right",
          animationDuration: 300
        });
      },
      logout() {
        uni.showModal({
          title: "退出登录",
          content: "确定要退出当前账号吗？",
          success: (res) => {
            if (res.confirm) {
              uni.redirectTo({
                url: "/pages/login/login"
              });
            }
          }
        });
      }
    }
  };
  function _sfc_render$7(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "profile-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("text", { class: "header-title" }, "个人中心")
      ]),
      vue.createCommentVNode(" 用户信息 "),
      vue.createElementVNode("view", { class: "profile-section" }, [
        vue.createElementVNode("view", { class: "user-info" }, [
          vue.createElementVNode("image", {
            class: "avatar",
            src: _imports_0,
            mode: "aspectFill"
          }),
          vue.createElementVNode("view", { class: "user-details" }, [
            vue.createElementVNode(
              "text",
              { class: "user-name" },
              vue.toDisplayString($data.userInfo.name),
              1
              /* TEXT */
            ),
            vue.createElementVNode(
              "text",
              { class: "user-id" },
              "工号: " + vue.toDisplayString($data.userInfo.employeeId),
              1
              /* TEXT */
            ),
            vue.createElementVNode(
              "text",
              { class: "user-phone" },
              vue.toDisplayString($data.userInfo.phone),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", {
            class: "edit-btn",
            onClick: _cache[0] || (_cache[0] = (...args) => $options.editProfile && $options.editProfile(...args))
          }, [
            vue.createElementVNode("text", { class: "edit-icon" }, "✏️")
          ])
        ]),
        vue.createElementVNode("view", { class: "stats-grid" }, [
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-value" },
              vue.toDisplayString($data.userInfo.rating),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "评分")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-value" },
              vue.toDisplayString($data.userInfo.completedTasks),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "完成任务")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-value" },
              vue.toDisplayString($data.userInfo.workDays),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "工作天数")
          ])
        ])
      ]),
      vue.createCommentVNode(" 功能菜单 "),
      vue.createElementVNode("view", { class: "menu-section" }, [
        vue.createElementVNode("view", {
          class: "menu-item",
          onClick: _cache[1] || (_cache[1] = ($event) => $options.goToPage("pages/profile/settings"))
        }, [
          vue.createElementVNode("text", { class: "menu-icon" }, "⚙️"),
          vue.createElementVNode("text", { class: "menu-text" }, "系统设置"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ]),
        vue.createElementVNode("view", {
          class: "menu-item",
          onClick: _cache[2] || (_cache[2] = ($event) => $options.goToPage("pages/profile/wallet"))
        }, [
          vue.createElementVNode("text", { class: "menu-icon" }, "💰"),
          vue.createElementVNode("text", { class: "menu-text" }, "我的钱包"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ]),
        vue.createElementVNode("view", {
          class: "menu-item",
          onClick: _cache[3] || (_cache[3] = ($event) => $options.goToPage("pages/profile/orders"))
        }, [
          vue.createElementVNode("text", { class: "menu-icon" }, "📦"),
          vue.createElementVNode("text", { class: "menu-text" }, "历史订单"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ]),
        vue.createElementVNode("view", {
          class: "menu-item",
          onClick: _cache[4] || (_cache[4] = ($event) => $options.goToPage("pages/profile/certificates"))
        }, [
          vue.createElementVNode("text", { class: "menu-icon" }, "📄"),
          vue.createElementVNode("text", { class: "menu-text" }, "证件管理"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ]),
        vue.createElementVNode("view", {
          class: "menu-item",
          onClick: _cache[5] || (_cache[5] = ($event) => $options.goToPage("pages/profile/help"))
        }, [
          vue.createElementVNode("text", { class: "menu-icon" }, "❓"),
          vue.createElementVNode("text", { class: "menu-text" }, "帮助中心"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ]),
        vue.createElementVNode("view", {
          class: "menu-item",
          onClick: _cache[6] || (_cache[6] = ($event) => $options.goToPage("pages/profile/about"))
        }, [
          vue.createElementVNode("text", { class: "menu-icon" }, "ℹ️"),
          vue.createElementVNode("text", { class: "menu-text" }, "关于我们"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ])
      ]),
      vue.createCommentVNode(" 退出登录 "),
      vue.createElementVNode("view", { class: "logout-section" }, [
        vue.createElementVNode("button", {
          class: "logout-btn",
          onClick: _cache[7] || (_cache[7] = (...args) => $options.logout && $options.logout(...args))
        }, "退出登录")
      ])
    ]);
  }
  const PagesProfileIndex = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["render", _sfc_render$7], ["__scopeId", "data-v-201c0da5"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/profile/index.vue"]]);
  const _sfc_main$7 = {
    components: {
      TaskList: PagesTaskList,
      StatisticsIndex: PagesStatisticsIndex,
      ProfileIndex: PagesProfileIndex
    },
    data() {
      return {
        currentIndex: 0,
        loadedPages: ["task"],
        // 默认加载第一个页面
        pageTitles: ["任务列表", "工作统计", "个人中心"]
      };
    },
    computed: {
      currentPageTitle() {
        return this.pageTitles[this.currentIndex];
      }
    },
    methods: {
      switchTab(index) {
        this.currentIndex = index;
        this.loadPage(index);
      },
      onSwiperChange(e) {
        this.currentIndex = e.detail.current;
        this.loadPage(this.currentIndex);
      },
      loadPage(index) {
        const pages = ["task", "statistics", "profile"];
        const page = pages[index];
        if (!this.loadedPages.includes(page)) {
          this.loadedPages.push(page);
        }
      }
    }
  };
  function _sfc_render$6(_ctx, _cache, $props, $setup, $data, $options) {
    const _component_task_list = vue.resolveComponent("task-list");
    const _component_statistics_index = vue.resolveComponent("statistics-index");
    const _component_profile_index = vue.resolveComponent("profile-index");
    return vue.openBlock(), vue.createElementBlock("view", { class: "main-container" }, [
      vue.createCommentVNode(" 顶部导航栏 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode(
          "text",
          { class: "header-title" },
          vue.toDisplayString($options.currentPageTitle),
          1
          /* TEXT */
        )
      ]),
      vue.createCommentVNode(" 滑动页面容器 "),
      vue.createElementVNode("swiper", {
        class: "swiper-container",
        current: $data.currentIndex,
        onChange: _cache[0] || (_cache[0] = (...args) => $options.onSwiperChange && $options.onSwiperChange(...args)),
        duration: 300,
        "skip-hidden-item-layout": true
      }, [
        vue.createCommentVNode(" 任务列表页面 "),
        vue.createElementVNode("swiper-item", null, [
          vue.createElementVNode("scroll-view", {
            "scroll-y": "true",
            class: "page-content"
          }, [
            $data.loadedPages.includes("task") || $data.currentIndex === 0 ? (vue.openBlock(), vue.createBlock(
              _component_task_list,
              {
                key: 0,
                ref: "taskList"
              },
              null,
              512
              /* NEED_PATCH */
            )) : (vue.openBlock(), vue.createElementBlock("view", {
              key: 1,
              class: "loading-placeholder"
            }, "加载中..."))
          ])
        ]),
        vue.createCommentVNode(" 统计页面 "),
        vue.createElementVNode("swiper-item", null, [
          vue.createElementVNode("scroll-view", {
            "scroll-y": "true",
            class: "page-content"
          }, [
            $data.loadedPages.includes("statistics") || $data.currentIndex === 1 ? (vue.openBlock(), vue.createBlock(
              _component_statistics_index,
              {
                key: 0,
                ref: "statistics"
              },
              null,
              512
              /* NEED_PATCH */
            )) : (vue.openBlock(), vue.createElementBlock("view", {
              key: 1,
              class: "loading-placeholder"
            }, "加载中..."))
          ])
        ]),
        vue.createCommentVNode(" 个人中心页面 "),
        vue.createElementVNode("swiper-item", null, [
          vue.createElementVNode("scroll-view", {
            "scroll-y": "true",
            class: "page-content"
          }, [
            $data.loadedPages.includes("profile") || $data.currentIndex === 2 ? (vue.openBlock(), vue.createBlock(
              _component_profile_index,
              {
                key: 0,
                ref: "profile"
              },
              null,
              512
              /* NEED_PATCH */
            )) : (vue.openBlock(), vue.createElementBlock("view", {
              key: 1,
              class: "loading-placeholder"
            }, "加载中..."))
          ])
        ])
      ], 40, ["current"]),
      vue.createCommentVNode(" 底部导航栏 "),
      vue.createElementVNode("view", { class: "tab-bar" }, [
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["tab-item", { active: $data.currentIndex === 0 }]),
            onClick: _cache[1] || (_cache[1] = ($event) => $options.switchTab(0))
          },
          [
            vue.createElementVNode("text", { class: "tab-icon" }, "📋"),
            vue.createElementVNode("text", { class: "tab-text" }, "任务")
          ],
          2
          /* CLASS */
        ),
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["tab-item", { active: $data.currentIndex === 1 }]),
            onClick: _cache[2] || (_cache[2] = ($event) => $options.switchTab(1))
          },
          [
            vue.createElementVNode("text", { class: "tab-icon" }, "📊"),
            vue.createElementVNode("text", { class: "tab-text" }, "统计")
          ],
          2
          /* CLASS */
        ),
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["tab-item", { active: $data.currentIndex === 2 }]),
            onClick: _cache[3] || (_cache[3] = ($event) => $options.switchTab(2))
          },
          [
            vue.createElementVNode("text", { class: "tab-icon" }, "👤"),
            vue.createElementVNode("text", { class: "tab-text" }, "我的")
          ],
          2
          /* CLASS */
        )
      ])
    ]);
  }
  const PagesMainIndex = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["render", _sfc_render$6], ["__scopeId", "data-v-d311227b"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/main/index.vue"]]);
  const _sfc_main$6 = {
    data() {
      return {
        themes: ["light", "dark", "system"],
        currentTheme: "system"
      };
    },
    methods: {
      switchTheme(theme) {
        this.currentTheme = theme;
        uni.setStorageSync("theme", theme);
        this.applyTheme();
      },
      applyTheme() {
        const theme = this.currentTheme;
        if (theme === "system") {
          document.body.classList.remove("light-theme", "dark-theme");
        } else {
          document.body.classList.add(`${theme}-theme`);
        }
      }
    },
    mounted() {
      const savedTheme = uni.getStorageSync("theme") || "system";
      this.switchTheme(savedTheme);
    }
  };
  function _sfc_render$5(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "theme-switcher" }, [
      (vue.openBlock(true), vue.createElementBlock(
        vue.Fragment,
        null,
        vue.renderList($data.themes, (theme, index) => {
          return vue.openBlock(), vue.createElementBlock("button", {
            key: index,
            class: vue.normalizeClass(["theme-btn", { active: $data.currentTheme === theme }]),
            onClick: ($event) => $options.switchTheme(theme)
          }, vue.toDisplayString(theme), 11, ["onClick"]);
        }),
        128
        /* KEYED_FRAGMENT */
      ))
    ]);
  }
  const ThemeSwitcher = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["render", _sfc_render$5], ["__scopeId", "data-v-d495b30c"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/components/ThemeSwitcher.vue"]]);
  const _sfc_main$5 = {
    components: {
      ThemeSwitcher
    },
    data() {
      return {
        task: {
          id: "T20230401001",
          status: "pickup",
          // pending, pickup, delivery, completed
          statusText: "待取件",
          statusClass: "status-pickup",
          statusProgress: 2,
          trackingNumber: "SF1234567890",
          courierCompany: "顺丰速运",
          packageType: "文件",
          packageWeight: "0.5kg",
          remark: "请轻拿轻放，谢谢！",
          customerName: "张先生",
          customerPhone: "138****8888",
          deliveryAddress: "北京市朝阳区某某街道某某小区1号楼101室",
          deliveryTime: "工作日 anytime",
          senderName: "李女士",
          senderPhone: "139****9999",
          pickupAddress: "北京市海淀区某某大厦202室",
          pickupTime: "2023-04-01 10:00-12:00"
        }
      };
    },
    methods: {
      goBack() {
        uni.navigateBack({
          animationType: "slide-out-right",
          animationDuration: 300
        });
      },
      startTask() {
        uni.showModal({
          title: "提示",
          content: "确认开始处理该任务？",
          success: (res) => {
            if (res.confirm) {
              uni.showToast({
                title: "任务开始处理",
                icon: "success"
              });
              this.task.status = "pickup";
              this.task.statusText = "待取件";
              this.task.statusClass = "status-pickup";
              this.task.statusProgress = 2;
            }
          }
        });
      },
      pickupPackage() {
        uni.showModal({
          title: "取件确认",
          content: "确认已取件？",
          success: (res) => {
            if (res.confirm) {
              uni.showToast({
                title: "取件成功",
                icon: "success"
              });
              this.task.status = "delivery";
              this.task.statusText = "待派送";
              this.task.statusClass = "status-delivery";
              this.task.statusProgress = 3;
            }
          }
        });
      },
      deliverPackage() {
        uni.showModal({
          title: "送达确认",
          content: "确认已送达？",
          success: (res) => {
            if (res.confirm) {
              uni.showToast({
                title: "送达成功",
                icon: "success"
              });
              this.task.status = "completed";
              this.task.statusText = "已完成";
              this.task.statusClass = "status-completed";
              this.task.statusProgress = 4;
            }
          }
        });
      },
      contactCustomer() {
        uni.showActionSheet({
          itemList: ["拨打电话", "发送短信"],
          success: (res) => {
            if (res.tapIndex === 0) {
              uni.makePhoneCall({
                phoneNumber: this.task.customerPhone
              });
            } else {
              uni.showToast({
                title: "短信功能开发中",
                icon: "none"
              });
            }
          }
        });
      },
      handleException() {
        uni.navigateTo({
          url: "/pages/task/exception?id=" + this.task.id
        });
      }
    }
  };
  function _sfc_render$4(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "task-detail-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "back-icon" }, "←"),
          vue.createElementVNode("text", { class: "back-text" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "任务详情"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createCommentVNode(" 任务状态 "),
      vue.createElementVNode("view", { class: "task-status-section" }, [
        vue.createElementVNode("view", { class: "status-header" }, [
          vue.createElementVNode(
            "text",
            { class: "task-id" },
            "任务单号: " + vue.toDisplayString($data.task.id),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            {
              class: vue.normalizeClass(["task-status", $data.task.statusClass])
            },
            vue.toDisplayString($data.task.statusText),
            3
            /* TEXT, CLASS */
          )
        ]),
        vue.createElementVNode("view", { class: "status-progress" }, [
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["progress-item", { active: $data.task.statusProgress >= 1 }])
            },
            [
              vue.createElementVNode("view", { class: "progress-icon" }, "1"),
              vue.createElementVNode("text", { class: "progress-text" }, "接单")
            ],
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["progress-line", { active: $data.task.statusProgress >= 2 }])
            },
            null,
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["progress-item", { active: $data.task.statusProgress >= 2 }])
            },
            [
              vue.createElementVNode("view", { class: "progress-icon" }, "2"),
              vue.createElementVNode("text", { class: "progress-text" }, "取件")
            ],
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["progress-line", { active: $data.task.statusProgress >= 3 }])
            },
            null,
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["progress-item", { active: $data.task.statusProgress >= 3 }])
            },
            [
              vue.createElementVNode("view", { class: "progress-icon" }, "3"),
              vue.createElementVNode("text", { class: "progress-text" }, "派送")
            ],
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["progress-line", { active: $data.task.statusProgress >= 4 }])
            },
            null,
            2
            /* CLASS */
          ),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["progress-item", { active: $data.task.statusProgress >= 4 }])
            },
            [
              vue.createElementVNode("view", { class: "progress-icon" }, "4"),
              vue.createElementVNode("text", { class: "progress-text" }, "完成")
            ],
            2
            /* CLASS */
          )
        ])
      ]),
      vue.createCommentVNode(" 快递信息 "),
      vue.createElementVNode("view", { class: "section" }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("text", { class: "section-title" }, "快递信息")
        ]),
        vue.createElementVNode("view", { class: "section-content" }, [
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "快递单号:"),
            vue.createElementVNode(
              "text",
              { class: "info-value" },
              vue.toDisplayString($data.task.trackingNumber),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "快递公司:"),
            vue.createElementVNode(
              "text",
              { class: "info-value" },
              vue.toDisplayString($data.task.courierCompany),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "物品类型:"),
            vue.createElementVNode(
              "text",
              { class: "info-value" },
              vue.toDisplayString($data.task.packageType),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "物品重量:"),
            vue.createElementVNode(
              "text",
              { class: "info-value" },
              vue.toDisplayString($data.task.packageWeight),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "备注信息:"),
            vue.createElementVNode(
              "text",
              { class: "info-value" },
              vue.toDisplayString($data.task.remark),
              1
              /* TEXT */
            )
          ])
        ])
      ]),
      vue.createCommentVNode(" 收货信息 "),
      vue.createElementVNode("view", { class: "section" }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("text", { class: "section-title" }, "收货信息")
        ]),
        vue.createElementVNode("view", { class: "section-content" }, [
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "收货人:"),
            vue.createElementVNode(
              "text",
              { class: "info-value" },
              vue.toDisplayString($data.task.customerName),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "联系电话:"),
            vue.createElementVNode(
              "text",
              { class: "info-value phone-number" },
              vue.toDisplayString($data.task.customerPhone),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "收货地址:"),
            vue.createElementVNode(
              "text",
              { class: "info-value" },
              vue.toDisplayString($data.task.deliveryAddress),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "配送时间:"),
            vue.createElementVNode(
              "text",
              { class: "info-value" },
              vue.toDisplayString($data.task.deliveryTime),
              1
              /* TEXT */
            )
          ])
        ])
      ]),
      vue.createCommentVNode(" 寄件信息 "),
      vue.createElementVNode("view", { class: "section" }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("text", { class: "section-title" }, "寄件信息")
        ]),
        vue.createElementVNode("view", { class: "section-content" }, [
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "寄件人:"),
            vue.createElementVNode(
              "text",
              { class: "info-value" },
              vue.toDisplayString($data.task.senderName),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "联系电话:"),
            vue.createElementVNode(
              "text",
              { class: "info-value phone-number" },
              vue.toDisplayString($data.task.senderPhone),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "寄件地址:"),
            vue.createElementVNode(
              "text",
              { class: "info-value" },
              vue.toDisplayString($data.task.pickupAddress),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "info-item" }, [
            vue.createElementVNode("text", { class: "info-label" }, "取件时间:"),
            vue.createElementVNode(
              "text",
              { class: "info-value" },
              vue.toDisplayString($data.task.pickupTime),
              1
              /* TEXT */
            )
          ])
        ])
      ]),
      vue.createCommentVNode(" 操作按钮 "),
      vue.createElementVNode("view", { class: "action-buttons" }, [
        $data.task.status === "pending" ? (vue.openBlock(), vue.createElementBlock("button", {
          key: 0,
          class: "action-btn primary",
          onClick: _cache[1] || (_cache[1] = (...args) => $options.startTask && $options.startTask(...args))
        }, "开始处理")) : vue.createCommentVNode("v-if", true),
        $data.task.status === "pickup" ? (vue.openBlock(), vue.createElementBlock("button", {
          key: 1,
          class: "action-btn primary",
          onClick: _cache[2] || (_cache[2] = (...args) => $options.pickupPackage && $options.pickupPackage(...args))
        }, "确认取件")) : vue.createCommentVNode("v-if", true),
        $data.task.status === "delivery" ? (vue.openBlock(), vue.createElementBlock("button", {
          key: 2,
          class: "action-btn primary",
          onClick: _cache[3] || (_cache[3] = (...args) => $options.deliverPackage && $options.deliverPackage(...args))
        }, "确认送达")) : vue.createCommentVNode("v-if", true),
        vue.createElementVNode("button", {
          class: "action-btn secondary",
          onClick: _cache[4] || (_cache[4] = (...args) => $options.contactCustomer && $options.contactCustomer(...args))
        }, "联系客户"),
        vue.createElementVNode("button", {
          class: "action-btn secondary",
          onClick: _cache[5] || (_cache[5] = (...args) => $options.handleException && $options.handleException(...args))
        }, "异常处理"),
        $data.task.status === "delivery" ? (vue.openBlock(), vue.createElementBlock("button", {
          key: 3,
          class: "action-btn secondary",
          onClick: _cache[6] || (_cache[6] = (...args) => _ctx.payShippingFee && _ctx.payShippingFee(...args))
        }, "支付运费")) : vue.createCommentVNode("v-if", true)
      ])
    ]);
  }
  const PagesTaskDetail = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["render", _sfc_render$4], ["__scopeId", "data-v-43b93a3d"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/task/detail.vue"]]);
  const _sfc_main$4 = {
    components: {
      ThemeSwitcher
    },
    data() {
      return {
        task: {
          id: "T20230401001",
          status: "pickup",
          statusText: "待取件",
          statusClass: "status-pickup",
          senderName: "李女士",
          senderPhone: "139****9999",
          pickupAddress: "北京市海淀区某某大厦202室",
          pickupTime: "2023-04-01 10:00-12:00"
        },
        pickupInfo: {
          trackingNumber: "",
          packageWeight: "",
          packageType: "document",
          remark: "",
          photos: []
        },
        packageTypes: [
          { label: "文件", value: "document" },
          { label: "电子产品", value: "electronics" },
          { label: "衣物", value: "clothing" },
          { label: "食品", value: "food" },
          { label: "其他", value: "other" }
        ]
      };
    },
    methods: {
      goBack() {
        uni.navigateBack();
      },
      selectPackageType(type) {
        this.pickupInfo.packageType = type;
      },
      uploadPhoto() {
        uni.chooseImage({
          count: 1,
          success: (res) => {
            this.pickupInfo.photos.push(res.tempFilePaths[0]);
          }
        });
      },
      removePhoto(index) {
        this.pickupInfo.photos.splice(index, 1);
      },
      contactSender() {
        uni.makePhoneCall({
          phoneNumber: this.task.senderPhone
        });
      },
      confirmPickup() {
        if (!this.pickupInfo.trackingNumber) {
          uni.showToast({
            title: "请输入快递单号",
            icon: "none"
          });
          return;
        }
        if (!this.pickupInfo.packageWeight) {
          uni.showToast({
            title: "请输入物品重量",
            icon: "none"
          });
          return;
        }
        uni.showModal({
          title: "确认取件",
          content: "请确认已成功取件并核对信息无误",
          success: (res) => {
            if (res.confirm) {
              uni.showLoading({
                title: "处理中..."
              });
              setTimeout(() => {
                uni.hideLoading();
                uni.showToast({
                  title: "取件成功",
                  icon: "success"
                });
                uni.navigateBack({
                  animationType: "slide-out-right",
                  animationDuration: 300
                });
              }, 1500);
            }
          }
        });
      }
    }
  };
  function _sfc_render$3(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "pickup-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "back-icon" }, "←"),
          vue.createElementVNode("text", { class: "back-text" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "取件操作"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createCommentVNode(" 任务信息 "),
      vue.createElementVNode("view", { class: "task-info" }, [
        vue.createElementVNode("view", { class: "task-header" }, [
          vue.createElementVNode(
            "text",
            { class: "task-id" },
            "任务单号: " + vue.toDisplayString($data.task.id),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            {
              class: vue.normalizeClass(["task-status", $data.task.statusClass])
            },
            vue.toDisplayString($data.task.statusText),
            3
            /* TEXT, CLASS */
          )
        ]),
        vue.createElementVNode("view", { class: "task-details" }, [
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "寄件人:"),
            vue.createElementVNode(
              "text",
              { class: "value" },
              vue.toDisplayString($data.task.senderName),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "联系电话:"),
            vue.createElementVNode(
              "text",
              { class: "value phone-number" },
              vue.toDisplayString($data.task.senderPhone),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "取件地址:"),
            vue.createElementVNode(
              "text",
              { class: "value" },
              vue.toDisplayString($data.task.pickupAddress),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "预约时间:"),
            vue.createElementVNode(
              "text",
              { class: "value" },
              vue.toDisplayString($data.task.pickupTime),
              1
              /* TEXT */
            )
          ])
        ])
      ]),
      vue.createCommentVNode(" 取件操作 "),
      vue.createElementVNode("view", { class: "pickup-section" }, [
        vue.createElementVNode("view", { class: "section-title" }, "取件操作"),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "快递单号"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              type: "text",
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $data.pickupInfo.trackingNumber = $event),
              placeholder: "请输入快递单号"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.pickupInfo.trackingNumber]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "物品重量"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              type: "digit",
              "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $data.pickupInfo.packageWeight = $event),
              placeholder: "请输入物品重量(kg)"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.pickupInfo.packageWeight]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "物品类型"),
          vue.createElementVNode("view", { class: "type-selector" }, [
            (vue.openBlock(true), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($data.packageTypes, (type) => {
                return vue.openBlock(), vue.createElementBlock("view", {
                  class: vue.normalizeClass(["type-item", { active: $data.pickupInfo.packageType === type.value }]),
                  key: type.value,
                  onClick: ($event) => $options.selectPackageType(type.value)
                }, vue.toDisplayString(type.label), 11, ["onClick"]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "备注信息"),
          vue.withDirectives(vue.createElementVNode(
            "textarea",
            {
              class: "form-textarea",
              "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $data.pickupInfo.remark = $event),
              placeholder: "请输入备注信息(可选)"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.pickupInfo.remark]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "实拍照片"),
          vue.createElementVNode("view", { class: "photo-upload" }, [
            (vue.openBlock(true), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($data.pickupInfo.photos, (photo, index) => {
                return vue.openBlock(), vue.createElementBlock("view", {
                  class: "photo-item",
                  key: index
                }, [
                  vue.createElementVNode("image", {
                    class: "photo",
                    src: photo,
                    mode: "aspectFill"
                  }, null, 8, ["src"]),
                  vue.createElementVNode("text", {
                    class: "remove-photo",
                    onClick: ($event) => $options.removePhoto(index)
                  }, "×", 8, ["onClick"])
                ]);
              }),
              128
              /* KEYED_FRAGMENT */
            )),
            $data.pickupInfo.photos.length < 3 ? (vue.openBlock(), vue.createElementBlock("view", {
              key: 0,
              class: "photo-upload-btn",
              onClick: _cache[4] || (_cache[4] = (...args) => $options.uploadPhoto && $options.uploadPhoto(...args))
            }, [
              vue.createElementVNode("text", { class: "upload-icon" }, "+"),
              vue.createElementVNode("text", { class: "upload-text" }, "上传照片")
            ])) : vue.createCommentVNode("v-if", true)
          ])
        ])
      ]),
      vue.createCommentVNode(" 操作按钮 "),
      vue.createElementVNode("view", { class: "action-buttons" }, [
        vue.createElementVNode("button", {
          class: "action-btn secondary",
          onClick: _cache[5] || (_cache[5] = (...args) => $options.contactSender && $options.contactSender(...args))
        }, "联系寄件人"),
        vue.createElementVNode("button", {
          class: "action-btn primary",
          onClick: _cache[6] || (_cache[6] = (...args) => $options.confirmPickup && $options.confirmPickup(...args))
        }, "确认取件")
      ])
    ]);
  }
  const PagesTaskPickup = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["render", _sfc_render$3], ["__scopeId", "data-v-eef9b941"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/task/pickup.vue"]]);
  const _sfc_main$3 = {
    components: {
      ThemeSwitcher
    },
    data() {
      return {
        task: {
          id: "T20230401001",
          status: "delivery",
          statusText: "待派送",
          statusClass: "status-delivery",
          customerName: "张先生",
          customerPhone: "138****8888",
          deliveryAddress: "北京市朝阳区某某街道某某小区1号楼101室",
          deliveryTime: "工作日 anytime"
        },
        deliveryInfo: {
          deliveryMethod: "door",
          // door: 上门派送, self: 自提
          pickupCode: "",
          recipientName: "",
          recipientId: "",
          remark: "",
          photos: []
        },
        deliveryMethods: [
          { label: "上门派送", value: "door" },
          { label: "自提", value: "self" }
        ]
      };
    },
    methods: {
      goBack() {
        uni.navigateBack();
      },
      selectDeliveryMethod(method) {
        this.deliveryInfo.deliveryMethod = method;
      },
      uploadPhoto() {
        uni.chooseImage({
          count: 1,
          success: (res) => {
            this.deliveryInfo.photos.push(res.tempFilePaths[0]);
          }
        });
      },
      removePhoto(index) {
        this.deliveryInfo.photos.splice(index, 1);
      },
      contactCustomer() {
        uni.makePhoneCall({
          phoneNumber: this.task.customerPhone
        });
      },
      confirmDelivery() {
        if (this.deliveryInfo.deliveryMethod === "self" && !this.deliveryInfo.pickupCode) {
          uni.showToast({
            title: "请输入收货码",
            icon: "none"
          });
          return;
        }
        if (this.deliveryInfo.deliveryMethod === "door") {
          if (!this.deliveryInfo.recipientName) {
            uni.showToast({
              title: "请输入收货人姓名",
              icon: "none"
            });
            return;
          }
          if (!this.deliveryInfo.recipientId) {
            uni.showToast({
              title: "请输入收货人证件号",
              icon: "none"
            });
            return;
          }
        }
        uni.showModal({
          title: "确认送达",
          content: "请确认已成功送达并核对信息无误",
          success: (res) => {
            if (res.confirm) {
              uni.showLoading({
                title: "处理中..."
              });
              setTimeout(() => {
                uni.hideLoading();
                uni.showToast({
                  title: "派送成功",
                  icon: "success"
                });
                uni.navigateBack({
                  animationType: "slide-out-right",
                  animationDuration: 300
                });
              }, 1500);
            }
          }
        });
      }
    }
  };
  function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "delivery-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "back-icon" }, "←"),
          vue.createElementVNode("text", { class: "back-text" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "派送操作"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createCommentVNode(" 任务信息 "),
      vue.createElementVNode("view", { class: "task-info" }, [
        vue.createElementVNode("view", { class: "task-header" }, [
          vue.createElementVNode(
            "text",
            { class: "task-id" },
            "任务单号: " + vue.toDisplayString($data.task.id),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            {
              class: vue.normalizeClass(["task-status", $data.task.statusClass])
            },
            vue.toDisplayString($data.task.statusText),
            3
            /* TEXT, CLASS */
          )
        ]),
        vue.createElementVNode("view", { class: "task-details" }, [
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "收货人:"),
            vue.createElementVNode(
              "text",
              { class: "value" },
              vue.toDisplayString($data.task.customerName),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "联系电话:"),
            vue.createElementVNode(
              "text",
              { class: "value phone-number" },
              vue.toDisplayString($data.task.customerPhone),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "收货地址:"),
            vue.createElementVNode(
              "text",
              { class: "value" },
              vue.toDisplayString($data.task.deliveryAddress),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "预约时间:"),
            vue.createElementVNode(
              "text",
              { class: "value" },
              vue.toDisplayString($data.task.deliveryTime),
              1
              /* TEXT */
            )
          ])
        ])
      ]),
      vue.createCommentVNode(" 派送操作 "),
      vue.createElementVNode("view", { class: "delivery-section" }, [
        vue.createElementVNode("view", { class: "section-title" }, "派送操作"),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "派送方式"),
          vue.createElementVNode("view", { class: "delivery-methods" }, [
            (vue.openBlock(true), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($data.deliveryMethods, (method) => {
                return vue.openBlock(), vue.createElementBlock("view", {
                  class: vue.normalizeClass(["method-item", { active: $data.deliveryInfo.deliveryMethod === method.value }]),
                  key: method.value,
                  onClick: ($event) => $options.selectDeliveryMethod(method.value)
                }, vue.toDisplayString(method.label), 11, ["onClick"]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ])
        ]),
        $data.deliveryInfo.deliveryMethod === "self" ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "form-group"
        }, [
          vue.createElementVNode("text", { class: "form-label" }, "收货码"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              type: "number",
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $data.deliveryInfo.pickupCode = $event),
              placeholder: "请输入收货码"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.deliveryInfo.pickupCode]
          ])
        ])) : vue.createCommentVNode("v-if", true),
        $data.deliveryInfo.deliveryMethod === "door" ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "form-group"
        }, [
          vue.createElementVNode("text", { class: "form-label" }, "收货人姓名"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              type: "text",
              "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $data.deliveryInfo.recipientName = $event),
              placeholder: "请输入收货人姓名"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.deliveryInfo.recipientName]
          ])
        ])) : vue.createCommentVNode("v-if", true),
        $data.deliveryInfo.deliveryMethod === "door" ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 2,
          class: "form-group"
        }, [
          vue.createElementVNode("text", { class: "form-label" }, "收货人证件号"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              type: "idcard",
              "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $data.deliveryInfo.recipientId = $event),
              placeholder: "请输入收货人身份证号"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.deliveryInfo.recipientId]
          ])
        ])) : vue.createCommentVNode("v-if", true),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "备注信息"),
          vue.withDirectives(vue.createElementVNode(
            "textarea",
            {
              class: "form-textarea",
              "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => $data.deliveryInfo.remark = $event),
              placeholder: "请输入备注信息(可选)"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.deliveryInfo.remark]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "实拍照片"),
          vue.createElementVNode("view", { class: "photo-upload" }, [
            (vue.openBlock(true), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($data.deliveryInfo.photos, (photo, index) => {
                return vue.openBlock(), vue.createElementBlock("view", {
                  class: "photo-item",
                  key: index
                }, [
                  vue.createElementVNode("image", {
                    class: "photo",
                    src: photo,
                    mode: "aspectFill"
                  }, null, 8, ["src"]),
                  vue.createElementVNode("text", {
                    class: "remove-photo",
                    onClick: ($event) => $options.removePhoto(index)
                  }, "×", 8, ["onClick"])
                ]);
              }),
              128
              /* KEYED_FRAGMENT */
            )),
            $data.deliveryInfo.photos.length < 3 ? (vue.openBlock(), vue.createElementBlock("view", {
              key: 0,
              class: "photo-upload-btn",
              onClick: _cache[5] || (_cache[5] = (...args) => $options.uploadPhoto && $options.uploadPhoto(...args))
            }, [
              vue.createElementVNode("text", { class: "upload-icon" }, "+"),
              vue.createElementVNode("text", { class: "upload-text" }, "上传照片")
            ])) : vue.createCommentVNode("v-if", true)
          ])
        ])
      ]),
      vue.createCommentVNode(" 操作按钮 "),
      vue.createElementVNode("view", { class: "action-buttons" }, [
        vue.createElementVNode("button", {
          class: "action-btn secondary",
          onClick: _cache[6] || (_cache[6] = (...args) => $options.contactCustomer && $options.contactCustomer(...args))
        }, "联系收货人"),
        vue.createElementVNode("button", {
          class: "action-btn primary",
          onClick: _cache[7] || (_cache[7] = (...args) => $options.confirmDelivery && $options.confirmDelivery(...args))
        }, "确认送达")
      ])
    ]);
  }
  const PagesTaskDelivery = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render$2], ["__scopeId", "data-v-f4f36846"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/task/delivery.vue"]]);
  const _sfc_main$2 = {
    components: {
      ThemeSwitcher
    },
    data() {
      return {
        task: {
          id: "T20230401001",
          status: "delivery",
          statusText: "待派送",
          statusClass: "status-delivery",
          trackingNumber: "SF1234567890",
          customerName: "张先生",
          customerPhone: "138****8888"
        },
        exceptionInfo: {
          type: "",
          description: "",
          photos: []
        },
        exceptionTypes: [
          { label: "地址错误", value: "address_error", icon: "📍" },
          { label: "联系不上", value: "contact_fail", icon: "📞" },
          { label: "拒收", value: "refuse", icon: "✋" },
          { label: "包装破损", value: "package_damage", icon: "📦" },
          { label: "物品丢失", value: "package_lost", icon: "🔍" },
          { label: "其他", value: "other", icon: "❓" }
        ]
      };
    },
    methods: {
      goBack() {
        uni.navigateBack();
      },
      selectExceptionType(type) {
        this.exceptionInfo.type = type;
      },
      uploadPhoto() {
        uni.chooseImage({
          count: 1,
          success: (res) => {
            this.exceptionInfo.photos.push(res.tempFilePaths[0]);
          }
        });
      },
      removePhoto(index) {
        this.exceptionInfo.photos.splice(index, 1);
      },
      contactCustomer() {
        uni.makePhoneCall({
          phoneNumber: this.task.customerPhone
        });
      },
      submitException() {
        if (!this.exceptionInfo.type) {
          uni.showToast({
            title: "请选择异常类型",
            icon: "none"
          });
          return;
        }
        if (!this.exceptionInfo.description) {
          uni.showToast({
            title: "请输入异常描述",
            icon: "none"
          });
          return;
        }
        uni.showModal({
          title: "提交异常",
          content: "确认提交异常信息？提交后任务将暂停处理",
          success: (res) => {
            if (res.confirm) {
              uni.showLoading({
                title: "提交中..."
              });
              setTimeout(() => {
                uni.hideLoading();
                uni.showToast({
                  title: "提交成功",
                  icon: "success"
                });
                uni.navigateBack({
                  animationType: "slide-out-right",
                  animationDuration: 300
                });
              }, 1500);
            }
          }
        });
      }
    }
  };
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "exception-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "back-icon" }, "←"),
          vue.createElementVNode("text", { class: "back-text" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "异常处理"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createCommentVNode(" 任务信息 "),
      vue.createElementVNode("view", { class: "task-info" }, [
        vue.createElementVNode("view", { class: "task-header" }, [
          vue.createElementVNode(
            "text",
            { class: "task-id" },
            "任务单号: " + vue.toDisplayString($data.task.id),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            {
              class: vue.normalizeClass(["task-status", $data.task.statusClass])
            },
            vue.toDisplayString($data.task.statusText),
            3
            /* TEXT, CLASS */
          )
        ]),
        vue.createElementVNode("view", { class: "task-details" }, [
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "快递单号:"),
            vue.createElementVNode(
              "text",
              { class: "value" },
              vue.toDisplayString($data.task.trackingNumber),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "收货人:"),
            vue.createElementVNode(
              "text",
              { class: "value" },
              vue.toDisplayString($data.task.customerName),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "联系电话:"),
            vue.createElementVNode(
              "text",
              { class: "value phone-number" },
              vue.toDisplayString($data.task.customerPhone),
              1
              /* TEXT */
            )
          ])
        ])
      ]),
      vue.createCommentVNode(" 异常类型选择 "),
      vue.createElementVNode("view", { class: "exception-section" }, [
        vue.createElementVNode("view", { class: "section-title" }, "异常类型"),
        vue.createElementVNode("view", { class: "exception-types" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.exceptionTypes, (type) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: vue.normalizeClass(["type-item", { active: $data.exceptionInfo.type === type.value }]),
                key: type.value,
                onClick: ($event) => $options.selectExceptionType(type.value)
              }, [
                vue.createElementVNode(
                  "text",
                  { class: "type-icon" },
                  vue.toDisplayString(type.icon),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "text",
                  { class: "type-label" },
                  vue.toDisplayString(type.label),
                  1
                  /* TEXT */
                )
              ], 10, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ]),
      vue.createCommentVNode(" 异常详情 "),
      vue.createElementVNode("view", { class: "exception-section" }, [
        vue.createElementVNode("view", { class: "section-title" }, "异常详情"),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "异常描述"),
          vue.withDirectives(vue.createElementVNode(
            "textarea",
            {
              class: "form-textarea",
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $data.exceptionInfo.description = $event),
              placeholder: "请详细描述异常情况..."
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.exceptionInfo.description]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "实拍照片"),
          vue.createElementVNode("view", { class: "photo-upload" }, [
            (vue.openBlock(true), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($data.exceptionInfo.photos, (photo, index) => {
                return vue.openBlock(), vue.createElementBlock("view", {
                  class: "photo-item",
                  key: index
                }, [
                  vue.createElementVNode("image", {
                    class: "photo",
                    src: photo,
                    mode: "aspectFill"
                  }, null, 8, ["src"]),
                  vue.createElementVNode("text", {
                    class: "remove-photo",
                    onClick: ($event) => $options.removePhoto(index)
                  }, "×", 8, ["onClick"])
                ]);
              }),
              128
              /* KEYED_FRAGMENT */
            )),
            $data.exceptionInfo.photos.length < 4 ? (vue.openBlock(), vue.createElementBlock("view", {
              key: 0,
              class: "photo-upload-btn",
              onClick: _cache[2] || (_cache[2] = (...args) => $options.uploadPhoto && $options.uploadPhoto(...args))
            }, [
              vue.createElementVNode("text", { class: "upload-icon" }, "+"),
              vue.createElementVNode("text", { class: "upload-text" }, "上传照片")
            ])) : vue.createCommentVNode("v-if", true)
          ])
        ])
      ]),
      vue.createCommentVNode(" 操作按钮 "),
      vue.createElementVNode("view", { class: "action-buttons" }, [
        vue.createElementVNode("button", {
          class: "action-btn secondary",
          onClick: _cache[3] || (_cache[3] = (...args) => $options.contactCustomer && $options.contactCustomer(...args))
        }, "联系客户"),
        vue.createElementVNode("button", {
          class: "action-btn primary",
          onClick: _cache[4] || (_cache[4] = (...args) => $options.submitException && $options.submitException(...args))
        }, "提交异常")
      ])
    ]);
  }
  const PagesTaskException = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$1], ["__scopeId", "data-v-41cb3bd2"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/task/exception.vue"]]);
  const _sfc_main$1 = {
    data() {
      return {
        task: {
          id: "T20230401001",
          status: "delivery",
          statusText: "待派送",
          statusClass: "status-delivery",
          customerName: "张先生",
          customerPhone: "138****8888",
          deliveryAddress: "北京市朝阳区某某街道某某小区1号楼101室",
          distance: "2.5km",
          packageWeight: "0.5kg",
          packageType: "文件"
        },
        fee: {
          baseFee: 8,
          distanceFee: 2,
          weightFee: 0,
          specialItemFee: 0,
          totalFee: 10
        },
        paymentInfo: {
          method: "wechat"
          // 默认微信支付
        },
        paymentMethods: [
          { name: "微信支付", value: "wechat", icon: "💬" },
          { name: "支付宝", value: "alipay", icon: "🔍" },
          { name: "银行卡", value: "bank", icon: "💳" },
          { name: "现金支付", value: "cash", icon: "💵" }
        ]
      };
    },
    methods: {
      goBack() {
        uni.navigateBack();
      },
      selectPaymentMethod(method) {
        this.paymentInfo.method = method;
      },
      confirmPayment() {
        uni.showModal({
          title: "确认支付",
          content: `确认支付运费¥${this.fee.totalFee}？`,
          success: (res) => {
            if (res.confirm) {
              uni.showLoading({
                title: "支付中..."
              });
              setTimeout(() => {
                uni.hideLoading();
                uni.showToast({
                  title: "支付成功",
                  icon: "success"
                });
                uni.navigateBack({
                  animationType: "slide-out-right",
                  animationDuration: 300
                });
              }, 1500);
            }
          }
        });
      }
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "payment-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 页面头部 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("view", {
          class: "header-left",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args))
        }, [
          vue.createElementVNode("text", { class: "back-icon" }, "←"),
          vue.createElementVNode("text", { class: "back-text" }, "返回")
        ]),
        vue.createElementVNode("text", { class: "header-title" }, "支付运费"),
        vue.createElementVNode("view", { class: "header-right" })
      ]),
      vue.createCommentVNode(" 任务信息 "),
      vue.createElementVNode("view", { class: "task-info" }, [
        vue.createElementVNode("view", { class: "task-header" }, [
          vue.createElementVNode(
            "text",
            { class: "task-id" },
            "任务单号: " + vue.toDisplayString($data.task.id),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            {
              class: vue.normalizeClass(["task-status", $data.task.statusClass])
            },
            vue.toDisplayString($data.task.statusText),
            3
            /* TEXT, CLASS */
          )
        ]),
        vue.createElementVNode("view", { class: "task-details" }, [
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "收货人:"),
            vue.createElementVNode(
              "text",
              { class: "value" },
              vue.toDisplayString($data.task.customerName),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "联系电话:"),
            vue.createElementVNode(
              "text",
              { class: "value phone-number" },
              vue.toDisplayString($data.task.customerPhone),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-item" }, [
            vue.createElementVNode("text", { class: "label" }, "收货地址:"),
            vue.createElementVNode(
              "text",
              { class: "value" },
              vue.toDisplayString($data.task.deliveryAddress),
              1
              /* TEXT */
            )
          ])
        ])
      ]),
      vue.createCommentVNode(" 运费信息 "),
      vue.createElementVNode("view", { class: "payment-section" }, [
        vue.createElementVNode("view", { class: "section-title" }, "运费详情"),
        vue.createElementVNode("view", { class: "fee-details" }, [
          vue.createElementVNode("view", { class: "fee-item" }, [
            vue.createElementVNode("text", { class: "fee-label" }, "基础运费"),
            vue.createElementVNode(
              "text",
              { class: "fee-value" },
              "¥" + vue.toDisplayString($data.fee.baseFee),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "fee-item" }, [
            vue.createElementVNode("text", { class: "fee-label" }, "距离附加费"),
            vue.createElementVNode(
              "text",
              { class: "fee-value" },
              "¥" + vue.toDisplayString($data.fee.distanceFee),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "fee-item" }, [
            vue.createElementVNode("text", { class: "fee-label" }, "重量附加费"),
            vue.createElementVNode(
              "text",
              { class: "fee-value" },
              "¥" + vue.toDisplayString($data.fee.weightFee),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "fee-item" }, [
            vue.createElementVNode("text", { class: "fee-label" }, "特殊物品费"),
            vue.createElementVNode(
              "text",
              { class: "fee-value" },
              "¥" + vue.toDisplayString($data.fee.specialItemFee),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "fee-divider" }),
          vue.createElementVNode("view", { class: "fee-item total" }, [
            vue.createElementVNode("text", { class: "fee-label" }, "总计"),
            vue.createElementVNode(
              "text",
              { class: "fee-value total-value" },
              "¥" + vue.toDisplayString($data.fee.totalFee),
              1
              /* TEXT */
            )
          ])
        ])
      ]),
      vue.createCommentVNode(" 支付方式 "),
      vue.createElementVNode("view", { class: "payment-section" }, [
        vue.createElementVNode("view", { class: "section-title" }, "支付方式"),
        vue.createElementVNode("view", { class: "payment-methods" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.paymentMethods, (method) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: vue.normalizeClass(["method-item", { active: $data.paymentInfo.method === method.value }]),
                key: method.value,
                onClick: ($event) => $options.selectPaymentMethod(method.value)
              }, [
                vue.createElementVNode(
                  "text",
                  { class: "method-icon" },
                  vue.toDisplayString(method.icon),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "text",
                  { class: "method-name" },
                  vue.toDisplayString(method.name),
                  1
                  /* TEXT */
                )
              ], 10, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ]),
      vue.createCommentVNode(" 操作按钮 "),
      vue.createElementVNode("view", { class: "action-buttons" }, [
        vue.createElementVNode("button", {
          class: "action-btn primary",
          onClick: _cache[1] || (_cache[1] = (...args) => $options.confirmPayment && $options.confirmPayment(...args))
        }, "确认支付")
      ])
    ]);
  }
  const PagesPaymentIndex = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__scopeId", "data-v-7695f594"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/payment/index.vue"]]);
  __definePage("pages/index/index", PagesIndexIndex);
  __definePage("pages/login/login", PagesLoginLogin);
  __definePage("pages/main/index", PagesMainIndex);
  __definePage("pages/task/list", PagesTaskList);
  __definePage("pages/task/detail", PagesTaskDetail);
  __definePage("pages/task/pickup", PagesTaskPickup);
  __definePage("pages/task/delivery", PagesTaskDelivery);
  __definePage("pages/task/exception", PagesTaskException);
  __definePage("pages/payment/index", PagesPaymentIndex);
  __definePage("pages/statistics/index", PagesStatisticsIndex);
  __definePage("pages/profile/index", PagesProfileIndex);
  const _sfc_main = {
    onLaunch: function() {
      formatAppLog("log", "at App.vue:4", "App Launch");
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:7", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:10", "App Hide");
    },
    globalData: {
      pageTransition: "slide"
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
