
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#3498db","disableScroll":true,"background":"#3498db","animationType":"slide-in-right","animationDuration":300,"navigationBar":{"backgroundColor":"#3498db","titleText":"快递员端","type":"default","titleColor":"#ffffff"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"sl-express-courier","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.76","entryPagePath":"pages/index/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#7A7E83","selectedColor":"#3498db","borderStyle":"black","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/main/index","iconPath":"/static/tabbar/task.png","selectedIconPath":"/static/tabbar/task-active.png","text":"主页"}],"backgroundColor":"#ffffff","selectedIndex":0,"shown":true},"locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/index/index","meta":{"isQuit":true,"isEntry":true,"disableScroll":true,"titleNView":false,"animationType":"slide-in-right","animationDuration":300,"navigationBar":{"titleText":"首页","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/login/login","meta":{"disableScroll":true,"titleNView":false,"subNVues":[],"animationType":"slide-in-right","animationDuration":300,"navigationBar":{"titleText":"","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/main/index","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":0,"disableScroll":true,"titleNView":false,"animationType":"slide-in-right","animationDuration":300,"navigationBar":{"titleText":"主页","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/task/list","meta":{"disableScroll":true,"titleNView":false,"animationType":"slide-in-right","animationDuration":300,"navigationBar":{"titleText":"任务列表","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/task/detail","meta":{"disableScroll":true,"titleNView":false,"animationType":"slide-in-right","animationDuration":300,"navigationBar":{"titleText":"任务详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/task/pickup","meta":{"disableScroll":true,"titleNView":false,"animationType":"slide-in-right","animationDuration":300,"navigationBar":{"titleText":"取件操作","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/task/delivery","meta":{"disableScroll":true,"titleNView":false,"animationType":"slide-in-right","animationDuration":300,"navigationBar":{"titleText":"派送操作","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/task/exception","meta":{"disableScroll":true,"titleNView":false,"animationType":"slide-in-right","animationDuration":300,"navigationBar":{"titleText":"异常处理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/payment/index","meta":{"disableScroll":true,"titleNView":false,"animationType":"slide-in-right","animationDuration":300,"navigationBar":{"titleText":"支付运费","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/statistics/index","meta":{"disableScroll":true,"titleNView":false,"animationType":"slide-in-right","animationDuration":300,"navigationBar":{"titleText":"工作统计","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/profile/index","meta":{"disableScroll":true,"titleNView":false,"animationType":"slide-in-right","animationDuration":300,"navigationBar":{"titleText":"个人中心","style":"custom","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  