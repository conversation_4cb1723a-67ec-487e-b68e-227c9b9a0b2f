<!-- 主题切换按钮组 - 适合移动端触摸操作 -->
<view class="theme-selector-container-right">
  <view class="theme-buttons">
    <button 
      :class="['theme-btn', { active: currentTheme === 'default' }]" 
      @click="switchTheme('default')"
    >
      默认
    </button>
    <button 
      :class="['theme-btn', { active: currentTheme === 'dark' }]" 
      @click="switchTheme('dark')"
    >
      暗色
    </button>
    <button 
      :class="['theme-btn', { active: currentTheme === 'green' }]" 
      @click="switchTheme('green')"
    >
      绿色
    </button>
    <button 
      :class="['theme-btn', { active: currentTheme === 'purple' }]" 
      @click="switchTheme('purple')"
    >
      紫色
    </button>
  </view>
</view>

<!-- 登录表单区域 -->
<view v-if="activeTab === 'account'" class="login-form">
  <!-- 用户名输入框 -->
  <input 
    type="text" 
    v-model="account.username" 
    placeholder="请输入账号"
    class="login-input"
  />
  
  <!-- 密码输入框 -->
  <input 
    :type="showPassword ? 'text' : 'password'"
    v-model="account.password" 
    placeholder="请输入密码"
    class="login-input"
    @keyup.enter="handleAccountLogin"
  />
  
  <!-- 显示或隐藏密码的图标 -->
  <view 
    @click="togglePasswordVisibility"
    :class="['eye-icon', { closed: !showPassword }]"
  ></view>
  
  <!-- 登录按钮 -->
  <button 
    :disabled="!isValidAccountLogin"
    @click="handleAccountLogin"
    class="login-button"
  >
    登录
  </button>
  
  <!-- 错误提示信息 -->
  <text v-if="accountError" class="error-message">{{ accountError }}</text>
</view>

<!-- 手机号登录表单 -->
<view v-else class="login-form">
  <!-- 手机号输入框 -->
  <input 
    type="text" 
    v-model="phone.phoneNumber" 
    placeholder="请输入手机号"
    maxlength="11"
    class="login-input"
  />
  
  <!-- 验证码输入框 -->
  <input 
    type="text" 
    v-model="phone.code" 
    placeholder="请输入验证码"
    maxlength="6"
    class="login-input verification-input"
  />
  
  <!-- 获取验证码按钮 -->
  <button 
    :disabled="countdown > 0"
    @click="getCode"
    class="verification-button"
  >
    {{ countdown > 0 ? `${countdown}秒后重发` : '获取验证码' }}
  </button>
  
  <!-- 登录按钮 -->
  <button 
    :disabled="!isValidPhoneLogin"
    @click="handlePhoneLogin"
    class="login-button"
  >
    登录
  </button>
  
  <!-- 错误提示信息 -->
  <text v-if="phoneError" class="error-message">{{ phoneError }}</text>
</view>