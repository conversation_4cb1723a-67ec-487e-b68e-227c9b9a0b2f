/* 右上角主题切换容器 */
.theme-selector-container-right {
  position: absolute;
  top: 80rpx;
  right: 20rpx;
  z-index: 10;
}

/* 主题按钮组容器 */
.theme-buttons {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 10rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
}

/* 主题按钮基础样式 */
.theme-btn {
  width: 120rpx;
  height: 50rpx;
  line-height: 50rpx;
  font-size: 24rpx;
  text-align: center;
  border: 1rpx solid var(--border-color);
  border-radius: 25rpx;
  background-color: white;
  color: var(--text-dark);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

/* 主题按钮悬停效果 */
.theme-btn:hover {
  background-color: var(--bg-light);
  border-color: var(--primary-color);
}

/* 主题按钮激活状态 */
.theme-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 0 10rpx var(--shadow-primary);
}