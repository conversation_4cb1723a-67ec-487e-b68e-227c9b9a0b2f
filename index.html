<!DOCTYPE html>
<!-- 应用程序HTML入口文件 -->
<html lang="zh-CN">
  <head>
    <!-- 字符编码设置 -->
    <meta charset="UTF-8" />
    <script>
      // 检测是否支持cover viewport
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      // 设置viewport meta标签，适配移动设备
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
    <!-- 页面标题 -->
    <title></title>
    <!-- 预加载链接占位符 -->
    <!--app-context-->
  </head>
  <body>
    <!-- 应用程序根元素 -->
    <div id="app"><!--app-html--></div>
    <!-- 应用程序入口脚本 -->
    <script type="module" src="/main.js"></script>
  </body>
</html>