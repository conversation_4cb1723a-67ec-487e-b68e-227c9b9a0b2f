{
    "pages": [
        // pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
        {
            "path": "pages/index/launch",
            "style": {
                "navigationBarTitleText": "",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "subNVues": [],
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/login/login",
            "style": {
                "navigationBarTitleText": "",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "subNVues": [],
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/main/index",
            "style": {
                "navigationBarTitleText": "主页",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/index/index",
            "style": {
                "navigationBarTitleText": "首页",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/task/list",
            "style": {
                "navigationBarTitleText": "任务列表",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/task/detail",
            "style": {
                "navigationBarTitleText": "任务详情",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/task/pickup",
            "style": {
                "navigationBarTitleText": "取件操作",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/task/delivery",
            "style": {
                "navigationBarTitleText": "派送操作",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/task/delivery-list",
            "style": {
                "navigationBarTitleText": "派件列表",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/task/exception",
            "style": {
                "navigationBarTitleText": "异常处理",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/task/all",
            "style": {
                "navigationBarTitleText": "全部取派",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/search/index",
            "style": {
                "navigationBarTitleText": "搜索",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/message/index",
            "style": {
                "navigationBarTitleText": "消息中心",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/freight/index",
            "style": {
                "navigationBarTitleText": "运费查询",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/exclusive/index",
            "style": {
                "navigationBarTitleText": "专属取寄",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/statistics/index",
            "style": {
                "navigationBarTitleText": "工作统计",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        },
        {
            "path": "pages/profile/index",
            "style": {
                "navigationBarTitleText": "个人中心",
                "navigationStyle": "custom",
                "disableScroll": true,
                "app-plus": {
                    "titleNView": false,
                    "animationType": "slide-in-right",
                    "animationDuration": 300
                }
            }
        }
    ],
    "globalStyle": {
        "navigationBarTextStyle": "white",
        "navigationBarTitleText": "快递员端",
        "navigationBarBackgroundColor": "#3498db",
        "backgroundColor": "#3498db",
        "disableScroll": true,
        "app-plus": {
            "background": "#3498db",
            "animationType": "slide-in-right",
            "animationDuration": 300
        }
    },
    "tabBar": {
        "color": "#7A7E83",
        "selectedColor": "#3498db",
        "borderStyle": "black",
        "backgroundColor": "#ffffff",
        "list": [
            {
                "pagePath": "pages/main/index",
                "iconPath": "static/tabbar/task.png",
                "selectedIconPath": "static/tabbar/task-active.png",
                "text": "首页"
            },
            {
                "pagePath": "pages/task/list",
                "iconPath": "static/tabbar/task.png",
                "selectedIconPath": "static/tabbar/task-active.png",
                "text": "取件"
            },
            {
                "pagePath": "pages/profile/index",
                "iconPath": "static/tabbar/profile.png",
                "selectedIconPath": "static/tabbar/profile-active.png",
                "text": "我的"
            }
        ]
    },
    "uniIdRouter": {}
}