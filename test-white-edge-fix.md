# 白边问题测试与修复方案

## 问题描述

在某些设备上，页面顶部或底部出现白边，影响用户体验。

## 测试方案

### 1. 安全区域适配检查
```css
/* 在 login.css 中检查以下设置 */
.login-container {
  padding-top: max(env(safe-area-inset-top), 40rpx);
}
```

### 2. 在不同设备上测试
- iPhone X/XR/XS (有刘海屏)
- iPhone 8/7 (传统屏幕)
- Android 设备

### 3. 检查项目
- [ ] 登录页面顶部无白边
- [ ] 状态栏区域显示正常
- [ ] 页面内容不被状态栏遮挡
- [ ] 主题切换功能正常
- [ ] 首页也无白边问题

## 可能需要的额外调整

如果仍有白边问题，可以尝试：

1. **调整状态栏高度变量**
```css
/* 在 login.css 中调整 */
.login-container {
  padding-top: calc(env(safe-area-inset-top) + 80rpx); /* 增加更多间距 */
}
```

2. **检查设备特定问题**
- 某些Android设备可能需要不同的配置
- 可以在 manifest.json 中调整 Android 特定设置

3. **使用条件编译**
```css
/* #ifdef APP-PLUS */
.login-container {
  /* App端特定样式 */
}
/* #endif */
```

## 注意事项

1. 修改后需要重新编译才能看到效果
2. 在HBuilderX中可能需要重启开发服务器
3. 真机测试效果可能与模拟器不同
4. 不同主题下都要测试状态栏显示效果